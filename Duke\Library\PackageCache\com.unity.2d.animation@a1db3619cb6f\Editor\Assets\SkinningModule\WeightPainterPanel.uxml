<?xml version="1.0" encoding="utf-8"?>
<UXML xmlns:ui="UnityEngine.UIElements" xmlns:eui="UnityEditor.UIElements" xmlns:aui="UnityEditor.U2D.Animation">
  <aui:WeightPainterPanel name="WeightPainterPanel" text="Weight Painter" picking-mode="Ignore">
    <ui:PopupWindow text="Weight Painter">
      <ui:VisualElement name ="BrushModeField" class="form-row">
        <ui:VisualElement class="form-popup" name="Mode" />
        <!-- No support for Enums outside of UnityEditor -->
        <!-- <eui:EnumField name="ModeField" type="WeightEditorMode" /> -->
      </ui:VisualElement>
      <ui:VisualElement name="Bone" class="form-row">
        <ui:VisualElement class="form-popup" name="BoneEnumPopup" />
        <!-- No factory method for PopupField here -->
      </ui:VisualElement>

      <ui:VisualElement class="form-row">
        <ui:Toggle name="NormalizeToggle" class="form-toggle" value="true" tooltip="Normalize weights after each edit" label="Normalize" />
      </ui:VisualElement>

      <ui:VisualElement name="Size" class="form-row">
        <eui:IntegerField name="SizeField" class="form-integerfield" value="25" label="Size" tooltip ="Brush size"/>
      </ui:VisualElement>

      <ui:VisualElement name="Amount" class="form-row">
        <ui:VisualElement class="form-editor">
          <ui:Slider name="AmountSlider" class="named-slider" direction="Horizontal" low-value="-1" high-value="1" label="Amount" tooltip ="Weight amount" />
          <eui:FloatField name="AmountField" class="slider-field" value="0" />
        </ui:VisualElement>
      </ui:VisualElement>
        <ui:VisualElement name="divider" />
        <aui:WeightInspectorIMGUIPanel name ="WeightsInspector" />
      <ui:VisualElement name="Hardness" class="form-row">
        <ui:VisualElement class="form-editor">
          <ui:Slider name="HardnessSlider" class="named-slider" direction="Horizontal" low-value="1" high-value="100" label="Hardness" tooltip ="Brush hardness" />
          <eui:IntegerField name="HardnessField" class="slider-field" value="1" />
        </ui:VisualElement>
      </ui:VisualElement>
      <ui:VisualElement name="Step" class="form-row">
        <ui:VisualElement class="form-editor">
          <ui:Slider name="StepSlider" class="named-slider" direction="Horizontal" low-value="1" high-value="100" label="Step" tooltip ="Brush step size" />
          <eui:IntegerField name="StepField" class="slider-field" value="1" />
        </ui:VisualElement>
      </ui:VisualElement>
    </ui:PopupWindow>
  </aui:WeightPainterPanel>
</UXML>
