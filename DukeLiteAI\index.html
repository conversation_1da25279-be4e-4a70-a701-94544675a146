<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DukeLiteAI Enhanced - Retro FPS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { background: #1a1a1f; font-family: 'Courier New', monospace; overflow: hidden; }
        #gameCanvas { display: block; background: linear-gradient(45deg, #2a2a2f 0%, #1a1a1f 100%); image-rendering: pixelated; }
        #ui { position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 10; }
        #hud { position: absolute; bottom: 20px; left: 20px; color: #ff9933; font-size: 18px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); font-weight: bold; }
        #weaponInfo { position: absolute; top: 20px; left: 20px; color: #ff9933; font-size: 16px; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); }
        #weaponSlots { position: absolute; bottom: 20px; right: 20px; display: flex; gap: 10px; }
        .weapon-slot { width: 50px; height: 50px; border: 2px solid #666; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px; font-weight: bold; }
        .weapon-slot.active { border-color: #ff9933; background: rgba(255,153,51,0.2); color: #ff9933; }
        #crosshair { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 20px; height: 20px; pointer-events: none; }
        .crosshair-line { position: absolute; background: #ff9933; opacity: 0.8; }
        .crosshair-h { width: 20px; height: 2px; top: 9px; left: 0; }
        .crosshair-v { width: 2px; height: 20px; top: 0; left: 9px; }
        #instructions { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ff9933; text-align: center; font-size: 18px; background: rgba(0,0,0,0.8); padding: 30px; border: 2px solid #ff9933; border-radius: 10px; z-index: 100; }
        #instructions h2 { color: #cc1a1a; margin-bottom: 20px; font-size: 24px; }
        #instructions p { margin: 10px 0; line-height: 1.4; }
        .controls { text-align: left; margin: 15px 0; }
        .hidden { display: none; }
        #startButton { background: #cc1a1a; color: white; border: none; padding: 15px 30px; font-size: 18px; font-family: 'Courier New', monospace; cursor: pointer; margin-top: 20px; border-radius: 5px; transition: background 0.3s; }
        #startButton:hover { background: #ff3333; }
        #damageOverlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 0, 0, 0); pointer-events: none; transition: background 0.1s; }
        #messages { position: absolute; top: 100px; left: 50%; transform: translateX(-50%); color: #ff9933; font-size: 16px; text-align: center; text-shadow: 2px 2px 4px rgba(0,0,0,0.8); }
        .message { background: rgba(0,0,0,0.7); padding: 10px 20px; margin: 5px 0; border-radius: 5px; animation: fadeInOut 3s ease-in-out; }
        @keyframes fadeInOut { 0% { opacity: 0; transform: translateY(-20px); } 20% { opacity: 1; transform: translateY(0); } 80% { opacity: 1; transform: translateY(0); } 100% { opacity: 0; transform: translateY(-20px); } }
        #gameCanvas { filter: contrast(1.2) brightness(1.1); }
    </style>
</head>
<body>
    <canvas id="gameCanvas"></canvas>
    <div id="ui">
        <div id="crosshair">
            <div class="crosshair-line crosshair-h"></div>
            <div class="crosshair-line crosshair-v"></div>
        </div>
        <div id="hud">
            <div>Health: <span id="health">100</span></div>
            <div>Ammo: <span id="ammo">30</span> / <span id="maxAmmo">30</span></div>
            <div id="reloadText" class="hidden" style="color: #cc1a1a;">RELOADING...</div>
        </div>
        <div id="weaponInfo">
            <div>Weapon: <span id="currentWeapon">Combat Rifle</span></div>
            <div>Damage: <span id="weaponDamage">25</span></div>
        </div>
        <div id="weaponSlots">
            <div class="weapon-slot" data-weapon="0">1</div>
            <div class="weapon-slot active" data-weapon="1">2</div>
            <div class="weapon-slot" data-weapon="2">3</div>
            <div class="weapon-slot" data-weapon="3">4</div>
            <div class="weapon-slot" data-weapon="4">5</div>
        </div>
        <div id="damageOverlay"></div>
        <div id="messages"></div>
    </div>
    <div id="instructions">
        <h2>🎮 DukeLiteAI Enhanced</h2>
        <p><strong>Welcome to the retro FPS experience!</strong></p>
        <div class="controls">
            <p><strong>CONTROLS:</strong></p>
            <p>• WASD - Move</p>
            <p>• Mouse - Look around</p>
            <p>• Left Click - Shoot</p>
            <p>• R - Reload</p>
            <p>• 1-5 - Switch weapons</p>
            <p>• Shift - Run</p>
            <p>• F - Pick up weapons</p>
        </div>
        <div class="controls">
            <p><strong>WEAPONS:</strong></p>
            <p>1. Pistol - Precise backup</p>
            <p>2. Rifle - Automatic workhorse</p>
            <p>3. Shotgun - Close-range devastation</p>
            <p>4. Sniper - Long-range elimination</p>
            <p>5. Plasma - Sci-fi energy weapon</p>
        </div>
        <button id="startButton">START GAME</button>
        <p style="font-size: 12px; margin-top: 15px; color: #999;">Click to lock mouse cursor for full FPS experience</p>
    </div>
    <script>
// DukeLiteAI Enhanced - Web-based Retro FPS
class RetroFPSGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.setupCanvas();
        
        this.gameStarted = false;
        this.mouseX = 0; this.mouseY = 0; this.keys = {};
        
        this.player = { x: 400, y: 300, angle: 0, health: 100, speed: 3, runSpeed: 5 };
        
        this.weapons = [
            { name: 'Pistol', damage: 35, ammo: 12, maxAmmo: 12, fireRate: 250, color: '#888' },
            { name: 'Combat Rifle', damage: 25, ammo: 30, maxAmmo: 30, fireRate: 100, color: '#ff9933' },
            { name: 'Shotgun', damage: 80, ammo: 8, maxAmmo: 8, fireRate: 667, color: '#cc1a1a' },
            { name: 'Sniper Rifle', damage: 100, ammo: 5, maxAmmo: 5, fireRate: 1250, color: '#33cc33' },
            { name: 'Plasma Rifle', damage: 40, ammo: 50, maxAmmo: 50, fireRate: 167, color: '#3399ff' }
        ];
        
        this.currentWeapon = 1; this.lastFireTime = 0; this.reloading = false;
        this.enemies = []; this.spawnEnemies();
        this.pickups = []; this.spawnPickups();
        this.muzzleFlashes = []; this.impacts = []; this.particles = [];
        this.colors = { wall: '#4d4d59', floor: '#663d26', ceiling: '#2a2a2f', ambient: '#ff9933', danger: '#cc1a1a', pickup: '#33ff33' };
        
        this.setupEventListeners(); this.updateUI(); this.gameLoop();
    }
    
    setupCanvas() {
        this.canvas.width = window.innerWidth; this.canvas.height = window.innerHeight;
        window.addEventListener('resize', () => { this.canvas.width = window.innerWidth; this.canvas.height = window.innerHeight; });
    }
    
    setupEventListeners() {
        document.getElementById('startButton').addEventListener('click', () => this.startGame());
        document.addEventListener('mousemove', (e) => { if (this.gameStarted) { this.mouseX = e.movementX || 0; this.mouseY = e.movementY || 0; this.player.angle += this.mouseX * 0.003; } });
        document.addEventListener('click', (e) => { if (this.gameStarted) this.fire(); });
        document.addEventListener('keydown', (e) => { this.keys[e.code] = true; if (this.gameStarted) { if (e.code >= 'Digit1' && e.code <= 'Digit5') this.switchWeapon(parseInt(e.code.slice(-1)) - 1); if (e.code === 'KeyR') this.reload(); if (e.code === 'KeyF') this.tryPickup(); } });
        document.addEventListener('keyup', (e) => { this.keys[e.code] = false; });
        document.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    startGame() {
        document.getElementById('instructions').classList.add('hidden'); this.gameStarted = true;
        this.canvas.requestPointerLock(); this.showMessage('🎮 Welcome to DukeLiteAI Enhanced!', 3000);
    }
    
    spawnEnemies() {
        for (let i = 0; i < 8; i++) {
            this.enemies.push({ x: Math.random() * (this.canvas.width - 100) + 50, y: Math.random() * (this.canvas.height - 100) + 50, health: 50, maxHealth: 50, size: 20, speed: 1, angle: Math.random() * Math.PI * 2, lastSeen: 0, state: 'patrol' });
        }
    }
    
    spawnPickups() {
        for (let i = 0; i < 5; i++) {
            this.pickups.push({ x: Math.random() * (this.canvas.width - 100) + 50, y: Math.random() * (this.canvas.height - 100) + 50, weapon: i, size: 15, rotation: 0, glowPhase: 0 });
        }
    }
    
    update() {
        if (!this.gameStarted) return;
        this.updatePlayer(); this.updateEnemies(); this.updatePickups(); this.updateEffects(); this.checkCollisions();
    }
    
    updatePlayer() {
        let speed = (this.keys['ShiftLeft'] || this.keys['ShiftRight']) ? this.player.runSpeed : this.player.speed;
        let dx = 0, dy = 0;
        if (this.keys['KeyW']) { dx += Math.cos(this.player.angle) * speed; dy += Math.sin(this.player.angle) * speed; }
        if (this.keys['KeyS']) { dx -= Math.cos(this.player.angle) * speed; dy -= Math.sin(this.player.angle) * speed; }
        if (this.keys['KeyA']) { dx += Math.cos(this.player.angle - Math.PI/2) * speed; dy += Math.sin(this.player.angle - Math.PI/2) * speed; }
        if (this.keys['KeyD']) { dx += Math.cos(this.player.angle + Math.PI/2) * speed; dy += Math.sin(this.player.angle + Math.PI/2) * speed; }
        this.player.x = Math.max(20, Math.min(this.canvas.width - 20, this.player.x + dx));
        this.player.y = Math.max(20, Math.min(this.canvas.height - 20, this.player.y + dy));
    }
    
    updateEnemies() {
        this.enemies.forEach((enemy, index) => {
            if (enemy.health <= 0) { this.enemies.splice(index, 1); return; }
            const distToPlayer = Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y);
            if (distToPlayer < 150) {
                enemy.state = 'chase';
                const angleToPlayer = Math.atan2(this.player.y - enemy.y, this.player.x - enemy.x);
                enemy.x += Math.cos(angleToPlayer) * enemy.speed; enemy.y += Math.sin(angleToPlayer) * enemy.speed;
            } else {
                enemy.state = 'patrol';
                enemy.x += Math.cos(enemy.angle) * enemy.speed * 0.5; enemy.y += Math.sin(enemy.angle) * enemy.speed * 0.5;
                if (Math.random() < 0.02) enemy.angle = Math.random() * Math.PI * 2;
            }
            if (enemy.x < 20 || enemy.x > this.canvas.width - 20 || enemy.y < 20 || enemy.y > this.canvas.height - 20) {
                enemy.angle = Math.random() * Math.PI * 2;
            }
        });
    }
    
    updatePickups() { this.pickups.forEach(pickup => { pickup.rotation += 0.02; pickup.glowPhase += 0.05; }); }
    
    updateEffects() {
        this.muzzleFlashes = this.muzzleFlashes.filter(flash => { flash.life -= 16; return flash.life > 0; });
        this.impacts = this.impacts.filter(impact => { impact.life -= 16; impact.size *= 0.95; return impact.life > 0; });
        this.particles = this.particles.filter(particle => { particle.x += particle.vx; particle.y += particle.vy; particle.life -= 16; particle.size *= 0.98; return particle.life > 0; });
    }
    
    checkCollisions() {
        this.enemies.forEach(enemy => { if (Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y) < 30) this.takeDamage(10); });
        this.pickups.forEach((pickup, index) => { if (Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y) < 40) this.showPickupPrompt(pickup); });
    }
    
    fire() {
        const weapon = this.weapons[this.currentWeapon]; const now = Date.now();
        if (now - this.lastFireTime < weapon.fireRate || weapon.ammo <= 0 || this.reloading) return;
        this.lastFireTime = now; weapon.ammo--;
        this.muzzleFlashes.push({ x: this.player.x + Math.cos(this.player.angle) * 30, y: this.player.y + Math.sin(this.player.angle) * 30, life: 100, color: weapon.color });
        this.castRay(); this.screenShake(); this.updateUI();
    }
    
    castRay() {
        const weapon = this.weapons[this.currentWeapon]; const rayLength = 500;
        const spread = weapon.name === 'Shotgun' ? 0.3 : 0.1; const pellets = weapon.name === 'Shotgun' ? 8 : 1;
        for (let i = 0; i < pellets; i++) {
            const angle = this.player.angle + (Math.random() - 0.5) * spread;
            const endX = this.player.x + Math.cos(angle) * rayLength; const endY = this.player.y + Math.sin(angle) * rayLength;
            this.enemies.forEach(enemy => {
                const dist = this.distanceToLine(this.player.x, this.player.y, endX, endY, enemy.x, enemy.y);
                if (dist < enemy.size) {
                    const damage = weapon.damage / pellets; enemy.health -= damage;
                    this.impacts.push({ x: enemy.x, y: enemy.y, life: 300, size: 20, color: '#ff4444' });
                    for (let j = 0; j < 5; j++) {
                        this.particles.push({ x: enemy.x, y: enemy.y, vx: (Math.random() - 0.5) * 4, vy: (Math.random() - 0.5) * 4, life: 500, size: 3, color: '#ff9933' });
                    }
                }
            });
        }
    }
    
    distanceToLine(x1, y1, x2, y2, px, py) {
        const A = px - x1; const B = py - y1; const C = x2 - x1; const D = y2 - y1;
        const dot = A * C + B * D; const lenSq = C * C + D * D;
        if (lenSq === 0) return Math.hypot(A, B);
        const param = dot / lenSq;
        let xx, yy;
        if (param < 0) { xx = x1; yy = y1; } else if (param > 1) { xx = x2; yy = y2; } else { xx = x1 + param * C; yy = y1 + param * D; }
        return Math.hypot(px - xx, py - yy);
    }
    
    reload() {
        const weapon = this.weapons[this.currentWeapon];
        if (weapon.ammo === weapon.maxAmmo || this.reloading) return;
        this.reloading = true; document.getElementById('reloadText').classList.remove('hidden');
        setTimeout(() => { weapon.ammo = weapon.maxAmmo; this.reloading = false; document.getElementById('reloadText').classList.add('hidden'); this.updateUI(); this.showMessage('Reloaded!', 1000); }, 2000);
    }
    
    switchWeapon(index) {
        if (index >= 0 && index < this.weapons.length) { this.currentWeapon = index; this.updateUI(); this.showMessage(`Switched to ${this.weapons[index].name}`, 1500); }
    }
    
    tryPickup() {
        this.pickups.forEach((pickup, index) => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 40) { const weapon = this.weapons[pickup.weapon]; weapon.ammo = weapon.maxAmmo; this.pickups.splice(index, 1); this.showMessage(`Picked up ${weapon.name}!`, 2000); this.updateUI(); }
        });
    }
    
    takeDamage(amount) {
        this.player.health = Math.max(0, this.player.health - amount);
        const overlay = document.getElementById('damageOverlay'); overlay.style.background = 'rgba(255, 0, 0, 0.3)';
        setTimeout(() => { overlay.style.background = 'rgba(255, 0, 0, 0)'; }, 200);
        this.updateUI(); if (this.player.health <= 0) this.gameOver();
    }
    
    screenShake() {
        const intensity = 5; const duration = 100;
        this.canvas.style.transform = `translate(${(Math.random() - 0.5) * intensity}px, ${(Math.random() - 0.5) * intensity}px)`;
        setTimeout(() => { this.canvas.style.transform = 'translate(0, 0)'; }, duration);
    }
    
    showMessage(text, duration) {
        const messagesDiv = document.getElementById('messages'); const messageDiv = document.createElement('div');
        messageDiv.className = 'message'; messageDiv.textContent = text; messagesDiv.appendChild(messageDiv);
        setTimeout(() => { if (messageDiv.parentNode) messageDiv.parentNode.removeChild(messageDiv); }, duration);
    }
    
    showPickupPrompt(pickup) { }
    
    updateUI() {
        const weapon = this.weapons[this.currentWeapon];
        document.getElementById('health').textContent = this.player.health;
        document.getElementById('ammo').textContent = weapon.ammo;
        document.getElementById('maxAmmo').textContent = weapon.maxAmmo;
        document.getElementById('currentWeapon').textContent = weapon.name;
        document.getElementById('weaponDamage').textContent = weapon.damage;
        document.querySelectorAll('.weapon-slot').forEach((slot, index) => { slot.classList.toggle('active', index === this.currentWeapon); });
    }
    
    gameOver() { this.gameStarted = false; this.showMessage('GAME OVER - Press F5 to restart', 10000); }
    
    render() {
        this.ctx.fillStyle = this.colors.floor; this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.drawRetroBackground();
        this.enemies.forEach(enemy => this.drawEnemy(enemy));
        this.pickups.forEach(pickup => this.drawPickup(pickup));
        this.muzzleFlashes.forEach(flash => this.drawMuzzleFlash(flash));
        this.impacts.forEach(impact => this.drawImpact(impact));
        this.particles.forEach(particle => this.drawParticle(particle));
    }
    
    drawRetroBackground() {
        this.ctx.strokeStyle = 'rgba(255, 153, 51, 0.1)'; this.ctx.lineWidth = 1; const gridSize = 50;
        for (let x = 0; x < this.canvas.width; x += gridSize) { this.ctx.beginPath(); this.ctx.moveTo(x, 0); this.ctx.lineTo(x, this.canvas.height); this.ctx.stroke(); }
        for (let y = 0; y < this.canvas.height; y += gridSize) { this.ctx.beginPath(); this.ctx.moveTo(0, y); this.ctx.lineTo(this.canvas.width, y); this.ctx.stroke(); }
    }
    
    drawEnemy(enemy) {
        this.ctx.save(); this.ctx.translate(enemy.x, enemy.y);
        this.ctx.fillStyle = enemy.state === 'chase' ? this.colors.danger : '#666';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
        const healthPercent = enemy.health / enemy.maxHealth;
        this.ctx.fillStyle = '#333'; this.ctx.fillRect(-enemy.size/2, -enemy.size/2 - 8, enemy.size, 4);
        this.ctx.fillStyle = healthPercent > 0.5 ? '#33ff33' : '#ff3333';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2 - 8, enemy.size * healthPercent, 4);
        this.ctx.restore();
    }
    
    drawPickup(pickup) {
        this.ctx.save(); this.ctx.translate(pickup.x, pickup.y); this.ctx.rotate(pickup.rotation);
        const glowIntensity = 0.5 + Math.sin(pickup.glowPhase) * 0.3;
        this.ctx.shadowColor = this.colors.pickup; this.ctx.shadowBlur = 20 * glowIntensity;
        const weapon = this.weapons[pickup.weapon]; this.ctx.fillStyle = weapon.color;
        this.ctx.fillRect(-pickup.size/2, -pickup.size/2, pickup.size, pickup.size);
        this.ctx.shadowBlur = 0; this.ctx.fillStyle = 'white'; this.ctx.font = '12px Courier New'; this.ctx.textAlign = 'center';
        this.ctx.fillText((pickup.weapon + 1).toString(), 0, 4); this.ctx.restore();
    }
    
    drawMuzzleFlash(flash) {
        this.ctx.save(); this.ctx.globalAlpha = flash.life / 100; this.ctx.fillStyle = flash.color; this.ctx.shadowColor = flash.color; this.ctx.shadowBlur = 20;
        this.ctx.beginPath(); this.ctx.arc(flash.x, flash.y, 15, 0, Math.PI * 2); this.ctx.fill(); this.ctx.restore();
    }
    
    drawImpact(impact) {
        this.ctx.save(); this.ctx.globalAlpha = impact.life / 300; this.ctx.fillStyle = impact.color; this.ctx.shadowColor = impact.color; this.ctx.shadowBlur = 10;
        this.ctx.beginPath(); this.ctx.arc(impact.x, impact.y, impact.size, 0, Math.PI * 2); this.ctx.fill(); this.ctx.restore();
    }
    
    drawParticle(particle) {
        this.ctx.save(); this.ctx.globalAlpha = particle.life / 500; this.ctx.fillStyle = particle.color;
        this.ctx.beginPath(); this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2); this.ctx.fill(); this.ctx.restore();
    }
    
    gameLoop() { this.update(); this.render(); requestAnimationFrame(() => this.gameLoop()); }
}

window.addEventListener('load', () => { new RetroFPSGame(); });
    </script>
</body>
</html>
