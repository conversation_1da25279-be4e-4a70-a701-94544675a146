<!DOCTYPE html>
<html>
<head>
    <title>Create Placeholder Assets</title>
</head>
<body>
    <h1>Creating Placeholder Assets for Duke Nukem 3D</h1>
    <canvas id="canvas" width="320" height="200" style="border: 1px solid black;"></canvas>
    <br>
    <button onclick="createWeaponSprites()">Create Weapon Sprites</button>
    <button onclick="createEnemySprites()">Create Enemy Sprites</button>
    <button onclick="createTextures()">Create Textures</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        function downloadCanvas(filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function createWeaponSprites() {
            // Create pistol sprite
            ctx.clearRect(0, 0, 320, 200);
            ctx.fillStyle = '#444444';
            ctx.fillRect(140, 120, 40, 60);
            ctx.fillStyle = '#666666';
            ctx.fillRect(150, 100, 20, 40);
            ctx.fillStyle = '#888888';
            ctx.fillRect(155, 95, 10, 15);
            downloadCanvas('pistol_idle.png');
            
            // Create shotgun sprite
            setTimeout(() => {
                ctx.clearRect(0, 0, 320, 200);
                ctx.fillStyle = '#654321';
                ctx.fillRect(130, 120, 60, 50);
                ctx.fillStyle = '#444444';
                ctx.fillRect(140, 100, 40, 30);
                ctx.fillStyle = '#888888';
                ctx.fillRect(145, 95, 30, 15);
                downloadCanvas('shotgun_idle.png');
            }, 500);
            
            // Create fist sprite
            setTimeout(() => {
                ctx.clearRect(0, 0, 320, 200);
                ctx.fillStyle = '#FFDBAC';
                ctx.beginPath();
                ctx.arc(160, 140, 30, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillRect(140, 140, 40, 40);
                downloadCanvas('fist_idle.png');
            }, 1000);
        }
        
        function createEnemySprites() {
            // Create demonario sprite
            canvas.width = 64;
            canvas.height = 64;
            ctx.clearRect(0, 0, 64, 64);
            ctx.fillStyle = '#ff4444';
            ctx.fillRect(16, 16, 32, 32);
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(20, 20, 8, 8);
            ctx.fillRect(36, 20, 8, 8);
            ctx.fillRect(24, 32, 16, 8);
            downloadCanvas('demonario_idle.png');
            
            // Create eviloogie sprite
            setTimeout(() => {
                ctx.clearRect(0, 0, 64, 64);
                ctx.fillStyle = '#44ff44';
                ctx.beginPath();
                ctx.arc(32, 32, 20, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(24, 24, 4, 4);
                ctx.fillRect(36, 24, 4, 4);
                downloadCanvas('eviloogie_idle.png');
            }, 500);
            
            // Create smorficus sprite
            setTimeout(() => {
                ctx.clearRect(0, 0, 64, 64);
                ctx.fillStyle = '#ff44ff';
                ctx.fillRect(12, 12, 40, 40);
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(20, 20, 6, 6);
                ctx.fillRect(38, 20, 6, 6);
                ctx.fillRect(24, 36, 16, 6);
                downloadCanvas('smorficus_idle.png');
                canvas.width = 320;
                canvas.height = 200;
            }, 1000);
        }
        
        function createTextures() {
            // Create wall texture
            canvas.width = 64;
            canvas.height = 64;
            ctx.clearRect(0, 0, 64, 64);
            
            // Create sci-fi wall pattern
            ctx.fillStyle = '#666666';
            ctx.fillRect(0, 0, 64, 64);
            ctx.fillStyle = '#888888';
            ctx.fillRect(4, 4, 56, 8);
            ctx.fillRect(4, 20, 56, 8);
            ctx.fillRect(4, 36, 56, 8);
            ctx.fillRect(4, 52, 56, 8);
            ctx.fillStyle = '#444444';
            ctx.fillRect(8, 0, 2, 64);
            ctx.fillRect(24, 0, 2, 64);
            ctx.fillRect(40, 0, 2, 64);
            ctx.fillRect(56, 0, 2, 64);
            downloadCanvas('wall_ship_0.png');
            
            // Create floor texture
            setTimeout(() => {
                ctx.clearRect(0, 0, 64, 64);
                ctx.fillStyle = '#333333';
                ctx.fillRect(0, 0, 64, 64);
                ctx.fillStyle = '#555555';
                for (let x = 0; x < 64; x += 8) {
                    for (let y = 0; y < 64; y += 8) {
                        if ((x + y) % 16 === 0) {
                            ctx.fillRect(x, y, 8, 8);
                        }
                    }
                }
                downloadCanvas('floor_ship.png');
                canvas.width = 320;
                canvas.height = 200;
            }, 500);
        }
    </script>
</body>
</html>
