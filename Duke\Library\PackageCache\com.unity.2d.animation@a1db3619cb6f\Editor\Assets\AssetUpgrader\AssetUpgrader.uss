/**********************************************************************************************************************/
/* AssetUpgrader                                                                                                      */
/**********************************************************************************************************************/

.unity-button-strip-field__button:checked {
    background-color: var(--unity-colors-highlight-background);
}

#ModeSelector {
    align-self: center;
    height: 28px;
    margin-top: 12px;
    margin-bottom: 12px;
}

#ToolDescription {
    white-space: normal;
}

#TopContainer {
    margin: 5px 10px 5px 10px;
}

#ConversionResultContainer {
    margin: 5px 10px 0 10px;
    min-height: 16px;
    flex-direction: row-reverse;
}

#ConversionResultContainer > Image {
    width: 20px;
    height: 16px;
    margin-right: 4px;
    margin-left: 4px;
}

#CenterContainer {
    margin: 5px 10px 5px 10px;
    height: 240px;
    border-width: 1px;
    border-color: var(--unity-colors-inspector_titlebar-border);
}

#CenterInfo {
    align-self: center;
    flex-grow: 1;
    white-space: normal;
    margin-top: 110px;
}

#ListHeader {
    align-content: center;
    flex-direction: row;
    min-height: 28px;
}

#SelectAll {
    margin-top: 5px;
    padding-right: 5px;
    border-right-width: 2px;
    border-right-color: var(--unity-colors-scrollbar_thumb-background);
}

#AssetHeader {
    flex: auto;
    margin-top: 8px;
    margin-left: 3px;
}

#AssetList {
    flex-direction: column;
}

#ListFooter {
    flex: 1;
    flex-direction: row-reverse;
}

#BottomContainer {
    margin: 5px 10px 5px 10px;
    min-height: 21px;
    flex-direction: row-reverse;
}

#InfoContainer {
    min-height: 40px;
}

.AssetCheckbox {
    padding-right: 5px;
}

.AssetElement {
    -unity-text-align: middle-left;
    flex: 1 1 auto;
}

.AssetRow {
    flex-direction: row;
}

.DarkArea {
    background-color: var(--unity-colors-inspector_titlebar-border_accent);
    width: 20px;
}

.AssetImage {
    width: 16px;
    height: 16px;
    align-self: center;
    margin-top: 7px;
}