// Duke Nukem 3D - Asset Manager
// Handles loading and managing all game assets (sprites, textures, sounds)

class AssetManager {
    constructor() {
        this.assets = {
            weapons: {},
            enemies: {},
            textures: {},
            sounds: {},
            ui: {},
            effects: {}
        };
        
        this.loadingPromises = [];
        this.loaded = false;
        
        // Asset definitions
        this.assetDefinitions = {
            weapons: {
                pistol: {
                    idle: 'assets/weapons/pistol_idle.svg',
                    fire: 'assets/weapons/pistol_fire.svg',
                    frames: 4,
                    frameWidth: 320,
                    frameHeight: 200
                },
                shotgun: { 
                    idle: 'assets/weapons/shotgun_idle.png',
                    fire: 'assets/weapons/shotgun_fire.gif',
                    frames: 6,
                    frameWidth: 320,
                    frameHeight: 200
                },
                fist: { 
                    idle: 'assets/weapons/fist_idle.png',
                    fire: 'assets/weapons/fist_punch.gif',
                    frames: 8,
                    frameWidth: 320,
                    frameHeight: 200
                },
                chaingun: {
                    idle: 'assets/weapons/chaingun_idle.png',
                    fire: 'assets/weapons/chaingun_fire.gif',
                    frames: 4,
                    frameWidth: 320,
                    frameHeight: 200
                }
            },
            
            enemies: {
                demonario: {
                    idle: 'assets/enemies/demonario_idle.svg',
                    walk: 'assets/enemies/demonario_idle.svg',
                    attack: 'assets/enemies/demonario_idle.svg',
                    death: 'assets/enemies/demonario_idle.svg',
                    frames: { idle: 1, walk: 4, attack: 3, death: 5 },
                    frameWidth: 64,
                    frameHeight: 64
                },
                eviloogie: {
                    idle: 'assets/enemies/eviloogie_idle.png',
                    walk: 'assets/enemies/eviloogie_walk.png',
                    attack: 'assets/enemies/eviloogie_attack.png',
                    death: 'assets/enemies/eviloogie_death.png',
                    frames: { idle: 1, walk: 4, attack: 3, death: 5 },
                    frameWidth: 64,
                    frameHeight: 64
                },
                smorficus: {
                    idle: 'assets/enemies/smorficus_idle.png',
                    walk: 'assets/enemies/smorficus_walk.png',
                    attack: 'assets/enemies/smorficus_attack.png',
                    death: 'assets/enemies/smorficus_death.png',
                    frames: { idle: 1, walk: 4, attack: 3, death: 5 },
                    frameWidth: 64,
                    frameHeight: 64
                }
            },
            
            textures: {
                walls: {
                    ship_0: 'assets/textures/wall_ship_0.svg',
                    ship_1: 'assets/textures/wall_ship_0.svg',
                    ship_2: 'assets/textures/wall_ship_0.svg',
                    ship_3: 'assets/textures/wall_ship_0.svg',
                    ship_4: 'assets/textures/wall_ship_0.svg',
                    desert_0: 'assets/textures/wall_ship_0.svg',
                    desert_1: 'assets/textures/wall_ship_0.svg'
                },
                floors: {
                    ship: 'assets/textures/floor_ship.png',
                    desert: 'assets/textures/floor_desert.png'
                },
                ceilings: {
                    default: 'assets/textures/ceiling.png'
                }
            },
            
            ui: {
                crosshair: 'assets/ui/crosshair.png',
                healthbar: 'assets/ui/healthbar.png',
                ammo_icons: 'assets/ui/ammo_icons.png'
            },
            
            effects: {
                muzzle_flash: 'assets/effects/muzzle_flash.png',
                blood_splat: 'assets/effects/blood_splat.png',
                explosion: 'assets/effects/explosion.png'
            }
        };
    }
    
    async loadAllAssets() {
        console.log('Loading Duke Nukem 3D assets...');
        
        // Load all asset categories
        await Promise.all([
            this.loadWeapons(),
            this.loadEnemies(),
            this.loadTextures(),
            this.loadUI(),
            this.loadEffects()
        ]);
        
        this.loaded = true;
        console.log('All assets loaded successfully!');
        return true;
    }
    
    async loadWeapons() {
        for (const [weaponName, weaponData] of Object.entries(this.assetDefinitions.weapons)) {
            this.assets.weapons[weaponName] = {};
            
            // Load idle and fire animations
            for (const [state, imagePath] of Object.entries(weaponData)) {
                if (state === 'frames' || state === 'frameWidth' || state === 'frameHeight') continue;
                
                try {
                    const img = await this.loadImage(imagePath);
                    this.assets.weapons[weaponName][state] = img;
                    this.assets.weapons[weaponName].frameData = {
                        frames: weaponData.frames,
                        frameWidth: weaponData.frameWidth,
                        frameHeight: weaponData.frameHeight
                    };
                } catch (error) {
                    console.warn(`Failed to load weapon asset: ${imagePath}, using fallback`);
                    this.assets.weapons[weaponName][state] = this.createFallbackWeapon(weaponName);
                }
            }
        }
    }
    
    async loadEnemies() {
        for (const [enemyName, enemyData] of Object.entries(this.assetDefinitions.enemies)) {
            this.assets.enemies[enemyName] = {};
            
            for (const [state, imagePath] of Object.entries(enemyData)) {
                if (state === 'frames' || state === 'frameWidth' || state === 'frameHeight') continue;
                
                try {
                    const img = await this.loadImage(imagePath);
                    this.assets.enemies[enemyName][state] = img;
                    this.assets.enemies[enemyName].frameData = {
                        frames: enemyData.frames,
                        frameWidth: enemyData.frameWidth,
                        frameHeight: enemyData.frameHeight
                    };
                } catch (error) {
                    console.warn(`Failed to load enemy asset: ${imagePath}, using fallback`);
                    this.assets.enemies[enemyName][state] = this.createFallbackEnemy(enemyName);
                }
            }
        }
    }
    
    async loadTextures() {
        for (const [category, textures] of Object.entries(this.assetDefinitions.textures)) {
            this.assets.textures[category] = {};
            
            for (const [textureName, imagePath] of Object.entries(textures)) {
                try {
                    const img = await this.loadImage(imagePath);
                    this.assets.textures[category][textureName] = img;
                } catch (error) {
                    console.warn(`Failed to load texture: ${imagePath}, using fallback`);
                    this.assets.textures[category][textureName] = this.createFallbackTexture(64, 64);
                }
            }
        }
    }
    
    async loadUI() {
        for (const [uiName, imagePath] of Object.entries(this.assetDefinitions.ui)) {
            try {
                const img = await this.loadImage(imagePath);
                this.assets.ui[uiName] = img;
            } catch (error) {
                console.warn(`Failed to load UI asset: ${imagePath}, using fallback`);
                this.assets.ui[uiName] = this.createFallbackUI(uiName);
            }
        }
    }
    
    async loadEffects() {
        for (const [effectName, imagePath] of Object.entries(this.assetDefinitions.effects)) {
            try {
                const img = await this.loadImage(imagePath);
                this.assets.effects[effectName] = img;
            } catch (error) {
                console.warn(`Failed to load effect: ${imagePath}, using fallback`);
                this.assets.effects[effectName] = this.createFallbackEffect(effectName);
            }
        }
    }
    
    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
            img.src = src;
        });
    }
    
    // Fallback asset creators for when files don't exist
    createFallbackWeapon(weaponName) {
        const canvas = document.createElement('canvas');
        canvas.width = 320;
        canvas.height = 200;
        const ctx = canvas.getContext('2d');
        
        // Draw a simple weapon representation
        ctx.fillStyle = '#666666';
        ctx.fillRect(140, 120, 40, 60);
        ctx.fillStyle = '#888888';
        ctx.fillRect(150, 100, 20, 40);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(weaponName.toUpperCase(), 160, 190);
        
        return canvas;
    }
    
    createFallbackEnemy(enemyName) {
        const canvas = document.createElement('canvas');
        canvas.width = 64;
        canvas.height = 64;
        const ctx = canvas.getContext('2d');
        
        // Draw a simple enemy representation
        ctx.fillStyle = '#ff6666';
        ctx.fillRect(16, 16, 32, 32);
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(20, 20, 8, 8);
        ctx.fillRect(36, 20, 8, 8);
        ctx.fillRect(24, 32, 16, 8);
        
        return canvas;
    }
    
    createFallbackTexture(width, height) {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        
        // Create a simple pattern
        ctx.fillStyle = '#444444';
        ctx.fillRect(0, 0, width, height);
        ctx.fillStyle = '#666666';
        for (let x = 0; x < width; x += 8) {
            for (let y = 0; y < height; y += 8) {
                if ((x + y) % 16 === 0) {
                    ctx.fillRect(x, y, 8, 8);
                }
            }
        }
        
        return canvas;
    }
    
    createFallbackUI(uiName) {
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');
        
        ctx.fillStyle = '#ffff00';
        ctx.fillRect(0, 0, 32, 32);
        ctx.fillStyle = '#000000';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(uiName, 16, 20);
        
        return canvas;
    }
    
    createFallbackEffect(effectName) {
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');
        
        ctx.fillStyle = '#ffff00';
        ctx.beginPath();
        ctx.arc(16, 16, 12, 0, Math.PI * 2);
        ctx.fill();
        
        return canvas;
    }
    
    // Getter methods
    getWeapon(weaponName, state = 'idle') {
        return this.assets.weapons[weaponName]?.[state] || this.createFallbackWeapon(weaponName);
    }
    
    getEnemy(enemyName, state = 'idle') {
        return this.assets.enemies[enemyName]?.[state] || this.createFallbackEnemy(enemyName);
    }
    
    getTexture(category, textureName) {
        return this.assets.textures[category]?.[textureName] || this.createFallbackTexture(64, 64);
    }
    
    getUI(uiName) {
        return this.assets.ui[uiName] || this.createFallbackUI(uiName);
    }
    
    getEffect(effectName) {
        return this.assets.effects[effectName] || this.createFallbackEffect(effectName);
    }
}

// Global asset manager instance
window.assetManager = new AssetManager();
