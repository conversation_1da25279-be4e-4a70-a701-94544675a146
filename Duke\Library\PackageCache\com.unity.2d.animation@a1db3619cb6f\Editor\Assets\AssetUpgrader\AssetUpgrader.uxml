<?xml version="1.0" encoding="utf-8"?>
<UXML xmlns="UnityEngine.UIElements" xmlns:e="UnityEditor.UIElements" xmlns:u2d="UnityEditor.U2D.Animation.Upgrading">
    <ScrollView>
        <VisualElement name="TopContainer">
            <u2d:ButtonStripField name="ModeSelector" />
            <Label name="ToolDescription" />
        </VisualElement>
        <VisualElement name="ConversionResultContainer">
            <Label name="SuccessCount" text="0" />
            <Image name="SuccessImage" />
            <Label name="ErrorCount" text="0" />
            <Image name="ErrorImage" />
            <Label name="WarningCount" text="0" />
            <Image name="WarningImage" />
        </VisualElement>
        <VisualElement name="CenterContainer">
            <Label name="CenterInfo" />
            <VisualElement name="ListHeader">
                <Toggle name="SelectAll" value="false" />
                <Label name="AssetHeader" />
                <VisualElement class="DarkArea"/>
            </VisualElement>
            <ListView name="AssetList" selection-type="Multiple" />
            <VisualElement name="ListFooter">
                <VisualElement class="DarkArea" />
            </VisualElement>
        </VisualElement>
        <VisualElement name="InfoContainer">
            <HelpBox name="InfoBox" />
        </VisualElement>
        <VisualElement name="BottomContainer">
            <Button name="OpenLog" text="Open upgrade log" />
            <Button name="UpgradeSelected" text="Upgrade selected" />
            <Button name="Scan" text="Scan project" />
        </VisualElement>
    </ScrollView>
</UXML>