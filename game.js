// DukeLiteAI Enhanced - Web-based Retro FPS
class RetroFPSGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.setupCanvas();
        
        // Game state
        this.gameStarted = false;
        this.mouseX = 0;
        this.mouseY = 0;
        this.keys = {};
        
        // Player properties
        this.player = {
            x: 400,
            y: 300,
            angle: 0,
            health: 100,
            speed: 3,
            runSpeed: 5
        };
        
        // Weapon system
        this.weapons = [
            { name: 'Pistol', damage: 35, ammo: 12, maxAmmo: 12, fireRate: 250, color: '#888' },
            { name: 'Combat Rifle', damage: 25, ammo: 30, maxAmmo: 30, fireRate: 100, color: '#ff9933' },
            { name: 'Shotgun', damage: 80, ammo: 8, maxAmmo: 8, fireRate: 667, color: '#cc1a1a' },
            { name: 'Sniper Rifle', damage: 100, ammo: 5, maxAmmo: 5, fireRate: 1250, color: '#33cc33' },
            { name: 'Plasma Rifle', damage: 40, ammo: 50, maxAmmo: 50, fireRate: 167, color: '#3399ff' }
        ];
        
        this.currentWeapon = 1;
        this.lastFireTime = 0;
        this.reloading = false;
        
        // Enemies
        this.enemies = [];
        this.maxEnemies = 8;
        this.enemyRespawnTimer = 0;
        this.enemyRespawnDelay = 5000; // 5 seconds
        this.spawnEnemies();
        
        // Weapon pickups
        this.pickups = [];
        this.spawnPickups();
        
        // Visual effects
        this.muzzleFlashes = [];
        this.impacts = [];
        this.particles = [];
        
        // Retro colors
        this.colors = {
            wall: '#4d4d59',
            floor: '#663d26',
            ceiling: '#2a2a2f',
            ambient: '#ff9933',
            danger: '#cc1a1a',
            pickup: '#33ff33'
        };
        
        this.setupEventListeners();
        this.updateUI();
        this.gameLoop();
    }
    
    setupCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        window.addEventListener('resize', () => {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
        });
    }
    
    setupEventListeners() {
        // Start game
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        // Mouse controls
        document.addEventListener('mousemove', (e) => {
            if (this.gameStarted) {
                this.mouseX = e.movementX || 0;
                this.mouseY = e.movementY || 0;
                this.player.angle += this.mouseX * 0.003;
            }
        });
        
        document.addEventListener('click', (e) => {
            if (this.gameStarted) {
                this.fire();
            }
        });
        
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            
            if (this.gameStarted) {
                // Weapon switching
                if (e.code >= 'Digit1' && e.code <= 'Digit5') {
                    const weaponIndex = parseInt(e.code.slice(-1)) - 1;
                    this.switchWeapon(weaponIndex);
                }
                
                // Reload
                if (e.code === 'KeyR') {
                    this.reload();
                }
                
                // Pickup
                if (e.code === 'KeyF') {
                    this.tryPickup();
                }
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // Prevent context menu
        document.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    startGame() {
        document.getElementById('instructions').classList.add('hidden');
        this.gameStarted = true;
        
        // Request pointer lock for FPS controls
        this.canvas.requestPointerLock();
        
        this.showMessage('🎮 Welcome to DukeLiteAI Enhanced!', 3000);
        this.showMessage('Lock and load, marine!', 2000);
    }
    
    spawnEnemies() {
        for (let i = 0; i < 8; i++) {
            this.spawnSingleEnemy();
        }
    }

    spawnSingleEnemy() {
        let attempts = 0;
        let validPosition = false;
        let x, y;

        // Try to find a valid spawn position (not too close to player or other enemies)
        while (!validPosition && attempts < 50) {
            x = Math.random() * (this.canvas.width - 100) + 50;
            y = Math.random() * (this.canvas.height - 100) + 50;

            // Check distance from player
            const distFromPlayer = Math.hypot(this.player.x - x, this.player.y - y);
            if (distFromPlayer < 100) {
                attempts++;
                continue;
            }

            // Check distance from other enemies
            let tooClose = false;
            for (let enemy of this.enemies) {
                const distFromEnemy = Math.hypot(enemy.x - x, enemy.y - y);
                if (distFromEnemy < 60) {
                    tooClose = true;
                    break;
                }
            }

            if (!tooClose) {
                validPosition = true;
            }
            attempts++;
        }

        // If we couldn't find a good position, use a random one anyway
        if (!validPosition) {
            x = Math.random() * (this.canvas.width - 100) + 50;
            y = Math.random() * (this.canvas.height - 100) + 50;
        }

        this.enemies.push({
            x: x,
            y: y,
            health: 50,
            maxHealth: 50,
            size: 20,
            speed: 1,
            angle: Math.random() * Math.PI * 2,
            lastSeen: 0,
            state: 'patrol' // patrol, chase, attack
        });
    }
    
    spawnPickups() {
        // Spawn weapon pickups
        for (let i = 0; i < 5; i++) {
            this.pickups.push({
                x: Math.random() * (this.canvas.width - 100) + 50,
                y: Math.random() * (this.canvas.height - 100) + 50,
                weapon: i,
                size: 15,
                rotation: 0,
                glowPhase: 0
            });
        }
    }
    
    update() {
        if (!this.gameStarted) return;
        
        this.updatePlayer();
        this.updateEnemies();
        this.updatePickups();
        this.updateEffects();
        this.checkCollisions();
    }
    
    updatePlayer() {
        let speed = this.player.speed;
        if (this.keys['ShiftLeft'] || this.keys['ShiftRight']) {
            speed = this.player.runSpeed;
        }
        
        let dx = 0, dy = 0;
        
        if (this.keys['KeyW']) {
            dx += Math.cos(this.player.angle) * speed;
            dy += Math.sin(this.player.angle) * speed;
        }
        if (this.keys['KeyS']) {
            dx -= Math.cos(this.player.angle) * speed;
            dy -= Math.sin(this.player.angle) * speed;
        }
        if (this.keys['KeyA']) {
            dx += Math.cos(this.player.angle - Math.PI/2) * speed;
            dy += Math.sin(this.player.angle - Math.PI/2) * speed;
        }
        if (this.keys['KeyD']) {
            dx += Math.cos(this.player.angle + Math.PI/2) * speed;
            dy += Math.sin(this.player.angle + Math.PI/2) * speed;
        }
        
        // Boundary checking
        this.player.x = Math.max(20, Math.min(this.canvas.width - 20, this.player.x + dx));
        this.player.y = Math.max(20, Math.min(this.canvas.height - 20, this.player.y + dy));
    }
    
    updateEnemies() {
        this.enemies.forEach((enemy, index) => {
            if (enemy.health <= 0) {
                this.enemies.splice(index, 1);
                return;
            }
            
            const distToPlayer = Math.hypot(
                this.player.x - enemy.x,
                this.player.y - enemy.y
            );
            
            // Simple AI state machine
            if (distToPlayer < 150) {
                enemy.state = 'chase';
                const angleToPlayer = Math.atan2(
                    this.player.y - enemy.y,
                    this.player.x - enemy.x
                );
                enemy.x += Math.cos(angleToPlayer) * enemy.speed;
                enemy.y += Math.sin(angleToPlayer) * enemy.speed;
            } else {
                enemy.state = 'patrol';
                enemy.x += Math.cos(enemy.angle) * enemy.speed * 0.5;
                enemy.y += Math.sin(enemy.angle) * enemy.speed * 0.5;
                
                // Random direction changes
                if (Math.random() < 0.02) {
                    enemy.angle = Math.random() * Math.PI * 2;
                }
            }
            
            // Boundary checking
            if (enemy.x < 20 || enemy.x > this.canvas.width - 20 ||
                enemy.y < 20 || enemy.y > this.canvas.height - 20) {
                enemy.angle = Math.random() * Math.PI * 2;
            }
        });
    }
    
    updatePickups() {
        this.pickups.forEach(pickup => {
            pickup.rotation += 0.02;
            pickup.glowPhase += 0.05;
        });
    }
    
    updateEffects() {
        // Update muzzle flashes
        this.muzzleFlashes = this.muzzleFlashes.filter(flash => {
            flash.life -= 16;
            return flash.life > 0;
        });
        
        // Update impact effects
        this.impacts = this.impacts.filter(impact => {
            impact.life -= 16;
            impact.size *= 0.95;
            return impact.life > 0;
        });
        
        // Update particles
        this.particles = this.particles.filter(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= 16;
            particle.size *= 0.98;
            return particle.life > 0;
        });
    }
    
    checkCollisions() {
        // Check enemy collisions with player
        this.enemies.forEach(enemy => {
            const dist = Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y);
            if (dist < 30) {
                this.takeDamage(10);
            }
        });
        
        // Check pickup collisions
        this.pickups.forEach((pickup, index) => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 40) {
                this.showPickupPrompt(pickup);
            }
        });
    }
    
    fire() {
        const weapon = this.weapons[this.currentWeapon];
        const now = Date.now();
        
        if (now - this.lastFireTime < weapon.fireRate || weapon.ammo <= 0 || this.reloading) {
            return;
        }
        
        this.lastFireTime = now;
        weapon.ammo--;
        
        // Create muzzle flash
        this.muzzleFlashes.push({
            x: this.player.x + Math.cos(this.player.angle) * 30,
            y: this.player.y + Math.sin(this.player.angle) * 30,
            life: 100,
            color: weapon.color
        });
        
        // Raycast for hit detection
        this.castRay();
        
        // Screen shake effect
        this.screenShake();
        
        this.updateUI();
    }
    
    castRay() {
        const weapon = this.weapons[this.currentWeapon];
        const rayLength = 500;
        const spread = weapon.name === 'Shotgun' ? 0.3 : 0.1;
        const pellets = weapon.name === 'Shotgun' ? 8 : 1;
        
        for (let i = 0; i < pellets; i++) {
            const angle = this.player.angle + (Math.random() - 0.5) * spread;
            const endX = this.player.x + Math.cos(angle) * rayLength;
            const endY = this.player.y + Math.sin(angle) * rayLength;
            
            // Check enemy hits
            this.enemies.forEach(enemy => {
                const dist = this.distanceToLine(
                    this.player.x, this.player.y,
                    endX, endY,
                    enemy.x, enemy.y
                );
                
                if (dist < enemy.size) {
                    const damage = weapon.damage / pellets;
                    enemy.health -= damage;
                    
                    // Create impact effect
                    this.impacts.push({
                        x: enemy.x,
                        y: enemy.y,
                        life: 300,
                        size: 20,
                        color: '#ff4444'
                    });
                    
                    // Create particles
                    for (let j = 0; j < 5; j++) {
                        this.particles.push({
                            x: enemy.x,
                            y: enemy.y,
                            vx: (Math.random() - 0.5) * 4,
                            vy: (Math.random() - 0.5) * 4,
                            life: 500,
                            size: 3,
                            color: '#ff9933'
                        });
                    }
                }
            });
        }
    }
    
    distanceToLine(x1, y1, x2, y2, px, py) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) return Math.hypot(A, B);
        
        const param = dot / lenSq;
        
        let xx, yy;
        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }
        
        return Math.hypot(px - xx, py - yy);
    }
    
    reload() {
        const weapon = this.weapons[this.currentWeapon];
        if (weapon.ammo === weapon.maxAmmo || this.reloading) return;
        
        this.reloading = true;
        document.getElementById('reloadText').classList.remove('hidden');
        
        setTimeout(() => {
            weapon.ammo = weapon.maxAmmo;
            this.reloading = false;
            document.getElementById('reloadText').classList.add('hidden');
            this.updateUI();
            this.showMessage('Reloaded!', 1000);
        }, 2000);
    }
    
    switchWeapon(index) {
        if (index >= 0 && index < this.weapons.length) {
            this.currentWeapon = index;
            this.updateUI();
            this.showMessage(`Switched to ${this.weapons[index].name}`, 1500);
        }
    }
    
    tryPickup() {
        this.pickups.forEach((pickup, index) => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 40) {
                const weapon = this.weapons[pickup.weapon];
                weapon.ammo = weapon.maxAmmo;
                this.pickups.splice(index, 1);
                this.showMessage(`Picked up ${weapon.name}!`, 2000);
                this.updateUI();
            }
        });
    }
    
    takeDamage(amount) {
        this.player.health = Math.max(0, this.player.health - amount);
        
        // Damage flash effect
        const overlay = document.getElementById('damageOverlay');
        overlay.style.background = 'rgba(255, 0, 0, 0.3)';
        setTimeout(() => {
            overlay.style.background = 'rgba(255, 0, 0, 0)';
        }, 200);
        
        this.updateUI();
        
        if (this.player.health <= 0) {
            this.gameOver();
        }
    }
    
    screenShake() {
        const intensity = 5;
        const duration = 100;
        
        this.canvas.style.transform = `translate(${(Math.random() - 0.5) * intensity}px, ${(Math.random() - 0.5) * intensity}px)`;
        
        setTimeout(() => {
            this.canvas.style.transform = 'translate(0, 0)';
        }, duration);
    }
    
    showMessage(text, duration) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.textContent = text;
        messagesDiv.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, duration);
    }
    
    showPickupPrompt(pickup) {
        // Remove any existing pickup prompts
        const existingPrompts = document.querySelectorAll('.pickup-prompt');
        existingPrompts.forEach(prompt => prompt.remove());

        // Create new pickup prompt
        const prompt = document.createElement('div');
        prompt.className = 'pickup-prompt';
        prompt.textContent = `Press F to pick up ${this.weapons[pickup.weapon].name}`;

        // Position the prompt near the pickup
        const rect = this.canvas.getBoundingClientRect();
        const screenX = (pickup.x / this.canvas.width) * rect.width + rect.left;
        const screenY = (pickup.y / this.canvas.height) * rect.height + rect.top - 30;

        prompt.style.left = screenX + 'px';
        prompt.style.top = screenY + 'px';

        document.body.appendChild(prompt);

        // Remove prompt after a short delay if player moves away
        setTimeout(() => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist >= 40) {
                prompt.remove();
            }
        }, 100);
    }
    
    updateUI() {
        const weapon = this.weapons[this.currentWeapon];
        
        document.getElementById('health').textContent = this.player.health;
        document.getElementById('ammo').textContent = weapon.ammo;
        document.getElementById('maxAmmo').textContent = weapon.maxAmmo;
        document.getElementById('currentWeapon').textContent = weapon.name;
        document.getElementById('weaponDamage').textContent = weapon.damage;
        
        // Update weapon slots
        document.querySelectorAll('.weapon-slot').forEach((slot, index) => {
            slot.classList.toggle('active', index === this.currentWeapon);
        });
    }
    
    gameOver() {
        this.gameStarted = false;
        this.showMessage('GAME OVER - Press F5 to restart', 10000);
    }
    
    render() {
        // Clear canvas with retro background
        this.ctx.fillStyle = this.colors.floor;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw retro grid pattern
        this.drawRetroBackground();
        
        // Draw enemies
        this.enemies.forEach(enemy => this.drawEnemy(enemy));
        
        // Draw pickups
        this.pickups.forEach(pickup => this.drawPickup(pickup));
        
        // Draw effects
        this.muzzleFlashes.forEach(flash => this.drawMuzzleFlash(flash));
        this.impacts.forEach(impact => this.drawImpact(impact));
        this.particles.forEach(particle => this.drawParticle(particle));
        
        // Draw player (for debugging)
        if (false) { // Set to true to see player position
            this.ctx.fillStyle = '#33ff33';
            this.ctx.beginPath();
            this.ctx.arc(this.player.x, this.player.y, 10, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }
    
    drawRetroBackground() {
        // Draw grid pattern for retro feel
        this.ctx.strokeStyle = 'rgba(255, 153, 51, 0.1)';
        this.ctx.lineWidth = 1;
        
        const gridSize = 50;
        for (let x = 0; x < this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y < this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
    
    drawEnemy(enemy) {
        // Draw enemy as retro-style cube
        this.ctx.save();
        this.ctx.translate(enemy.x, enemy.y);
        
        // Main body
        this.ctx.fillStyle = enemy.state === 'chase' ? this.colors.danger : '#666';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
        
        // Health bar
        const healthPercent = enemy.health / enemy.maxHealth;
        this.ctx.fillStyle = '#333';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2 - 8, enemy.size, 4);
        this.ctx.fillStyle = healthPercent > 0.5 ? '#33ff33' : '#ff3333';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2 - 8, enemy.size * healthPercent, 4);
        
        this.ctx.restore();
    }
    
    drawPickup(pickup) {
        this.ctx.save();
        this.ctx.translate(pickup.x, pickup.y);
        this.ctx.rotate(pickup.rotation);
        
        // Glow effect
        const glowIntensity = 0.5 + Math.sin(pickup.glowPhase) * 0.3;
        this.ctx.shadowColor = this.colors.pickup;
        this.ctx.shadowBlur = 20 * glowIntensity;
        
        // Draw weapon pickup
        const weapon = this.weapons[pickup.weapon];
        this.ctx.fillStyle = weapon.color;
        this.ctx.fillRect(-pickup.size/2, -pickup.size/2, pickup.size, pickup.size);
        
        // Draw weapon number
        this.ctx.shadowBlur = 0;
        this.ctx.fillStyle = 'white';
        this.ctx.font = '12px Courier New';
        this.ctx.textAlign = 'center';
        this.ctx.fillText((pickup.weapon + 1).toString(), 0, 4);
        
        this.ctx.restore();
    }
    
    drawMuzzleFlash(flash) {
        this.ctx.save();
        this.ctx.globalAlpha = flash.life / 100;
        this.ctx.fillStyle = flash.color;
        this.ctx.shadowColor = flash.color;
        this.ctx.shadowBlur = 20;
        
        this.ctx.beginPath();
        this.ctx.arc(flash.x, flash.y, 15, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    drawImpact(impact) {
        this.ctx.save();
        this.ctx.globalAlpha = impact.life / 300;
        this.ctx.fillStyle = impact.color;
        this.ctx.shadowColor = impact.color;
        this.ctx.shadowBlur = 10;
        
        this.ctx.beginPath();
        this.ctx.arc(impact.x, impact.y, impact.size, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    drawParticle(particle) {
        this.ctx.save();
        this.ctx.globalAlpha = particle.life / 500;
        this.ctx.fillStyle = particle.color;
        
        this.ctx.beginPath();
        this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }
}

// Start the game when page loads
window.addEventListener('load', () => {
    new RetroFPSGame();
});
