// Duke Nukem 3D - Web Edition
// Complete FPS Game Engine

class DukeNukem3D {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.setupCanvas();

        // Game state management
        this.gameState = 'menu'; // menu, loading, playing, paused, gameOver
        this.gameStarted = false;
        this.level = 1;
        this.score = 0;
        this.kills = 0;

        // Input handling
        this.keys = {};
        this.mouse = { x: 0, y: 0, sensitivity: 0.003 };
        this.pointerLocked = false;

        // Player system
        this.player = {
            x: 400,
            y: 300,
            z: 0, // For jumping/crouching
            angle: 0,
            health: 100,
            maxHealth: 100,
            armor: 0,
            maxArmor: 200,
            speed: 4,
            runSpeed: 7,
            jumpSpeed: 8,
            crouchSpeed: 2,
            isRunning: false,
            isJumping: false,
            isCrouching: false,
            jumpVelocity: 0,
            onGround: true
        };

        // Comprehensive weapon system
        this.weapons = [
            {
                id: 0,
                name: 'MIGHTY FOOT',
                damage: 50,
                ammo: -1, // Infinite
                maxAmmo: -1,
                fireRate: 500,
                range: 50,
                type: 'melee',
                sprite: 'foot',
                sound: 'kick'
            },
            {
                id: 1,
                name: 'PISTOL',
                damage: 25,
                ammo: 12,
                maxAmmo: 200,
                fireRate: 300,
                range: 800,
                type: 'hitscan',
                sprite: 'pistol',
                sound: 'pistol_fire',
                reloadTime: 1500
            },
            {
                id: 2,
                name: 'SHOTGUN',
                damage: 80,
                ammo: 8,
                maxAmmo: 50,
                fireRate: 800,
                range: 400,
                type: 'hitscan',
                pellets: 7,
                spread: 0.4,
                sprite: 'shotgun',
                sound: 'shotgun_fire',
                reloadTime: 2000
            },
            {
                id: 3,
                name: 'CHAINGUN',
                damage: 15,
                ammo: 50,
                maxAmmo: 200,
                fireRate: 80,
                range: 600,
                type: 'hitscan',
                sprite: 'chaingun',
                sound: 'chaingun_fire',
                spinUp: true
            },
            {
                id: 4,
                name: 'RPG',
                damage: 150,
                ammo: 5,
                maxAmmo: 50,
                fireRate: 1500,
                range: 1000,
                type: 'projectile',
                sprite: 'rpg',
                sound: 'rpg_fire',
                explosionRadius: 100
            },
            {
                id: 5,
                name: 'PIPE BOMBS',
                damage: 200,
                ammo: 3,
                maxAmmo: 50,
                fireRate: 1000,
                range: 500,
                type: 'explosive',
                sprite: 'pipebomb',
                sound: 'pipebomb_throw',
                timer: 3000
            },
            {
                id: 6,
                name: 'SHRINKER',
                damage: 1,
                ammo: 20,
                maxAmmo: 50,
                fireRate: 1200,
                range: 600,
                type: 'special',
                sprite: 'shrinker',
                sound: 'shrinker_fire',
                effect: 'shrink'
            },
            {
                id: 7,
                name: 'DEVASTATOR',
                damage: 120,
                ammo: 10,
                maxAmmo: 100,
                fireRate: 400,
                range: 800,
                type: 'projectile',
                sprite: 'devastator',
                sound: 'devastator_fire',
                dualFire: true,
                explosionRadius: 80
            },
            {
                id: 8,
                name: 'FREEZER',
                damage: 50,
                ammo: 25,
                maxAmmo: 100,
                fireRate: 600,
                range: 400,
                type: 'special',
                sprite: 'freezer',
                sound: 'freezer_fire',
                effect: 'freeze'
            }
        ];

        this.currentWeapon = 1; // Start with pistol
        this.weaponInventory = [true, true, false, false, false, false, false, false, false]; // Which weapons player has
        this.lastFireTime = 0;
        this.reloading = false;
        this.weaponBobbing = 0;
        this.weaponRecoil = 0;
        
        // Enemy system with diverse types
        this.enemies = [];
        this.maxEnemies = 12;
        this.enemyRespawnTimer = 0;
        this.enemyRespawnDelay = 8000; // 8 seconds between respawns
        this.enemiesKilled = 0;
        this.enemyTypes = {
            pigCop: {
                name: 'PIG COP',
                health: 80,
                speed: 2,
                damage: 15,
                size: 25,
                color: '#ff6666',
                ai: 'aggressive',
                weapon: 'shotgun',
                points: 100
            },
            alien: {
                name: 'ALIEN DRONE',
                health: 60,
                speed: 3,
                damage: 20,
                size: 20,
                color: '#66ff66',
                ai: 'flying',
                weapon: 'plasma',
                points: 150
            },
            octabrain: {
                name: 'OCTABRAIN',
                health: 120,
                speed: 1.5,
                damage: 30,
                size: 35,
                color: '#ff66ff',
                ai: 'psychic',
                weapon: 'mind',
                points: 200
            },
            enforcer: {
                name: 'ENFORCER',
                health: 150,
                speed: 1,
                damage: 40,
                size: 40,
                color: '#6666ff',
                ai: 'tank',
                weapon: 'chaingun',
                points: 300
            },
            boss: {
                name: 'BATTLELORD',
                health: 500,
                speed: 0.5,
                damage: 60,
                size: 60,
                color: '#ff0000',
                ai: 'boss',
                weapon: 'devastator',
                points: 1000
            }
        };

        // Pickups and power-ups
        this.pickups = [];
        this.pickupTypes = {
            health: { name: 'HEALTH PACK', value: 25, color: '#ff0000', size: 15 },
            armor: { name: 'ARMOR', value: 50, color: '#0066ff', size: 15 },
            ammo: { name: 'AMMO', value: 1, color: '#ffff00', size: 12 },
            weapon: { name: 'WEAPON', value: 1, color: '#00ff00', size: 18 },
            powerup: { name: 'POWER-UP', value: 1, color: '#ff00ff', size: 20 }
        };

        // Visual effects system
        this.effects = {
            muzzleFlashes: [],
            explosions: [],
            impacts: [],
            particles: [],
            blood: [],
            smoke: []
        };

        // Level and environment
        this.level = {
            width: 1200,
            height: 800,
            walls: [],
            doors: [],
            switches: [],
            secrets: []
        };

        // Audio system
        this.sounds = {
            enabled: true,
            volume: 0.7,
            music: null,
            effects: {}
        };

        // Duke's one-liners
        this.dukeQuotes = [
            "Hail to the king, baby!",
            "Come get some!",
            "I'm gonna rip your head off and shit down your neck!",
            "It's time to kick ass and chew bubblegum... and I'm all outta gum!",
            "Damn, I'm good!",
            "Groovy!",
            "Let God sort 'em out!",
            "Nobody steals our chicks... and lives!",
            "Your face, your ass - what's the difference?",
            "Blow it out your ass!",
            "I ain't afraid of no quake!",
            "Shake it, baby!",
            "What are you waiting for? Christmas?",
            "Die, you son of a bitch!",
            "Eat shit and die!"
        ];

        this.initialize();
    }
    
    initialize() {
        console.log('Initializing Duke Nukem 3D...');

        try {
            this.setupCanvas();
            this.setupEventListeners();
            this.generateLevel();
            this.spawnEnemies();
            this.spawnPickups();
            this.updateUI();
            this.gameLoop();

            console.log('Initialization complete!');
        } catch (error) {
            console.error('Initialization failed:', error);
        }
    }

    setupCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight - 80; // Account for status bar

        window.addEventListener('resize', () => {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight - 80;
        });

        // Set up canvas for retro pixel art
        this.ctx.imageSmoothingEnabled = false;
        this.ctx.webkitImageSmoothingEnabled = false;
        this.ctx.mozImageSmoothingEnabled = false;
        this.ctx.msImageSmoothingEnabled = false;
    }

    generateLevel() {
        // Generate a simple level layout
        this.level.width = this.canvas.width;
        this.level.height = this.canvas.height;

        // Create boundary walls
        this.level.walls = [
            { x1: 0, y1: 0, x2: this.level.width, y2: 0 }, // Top
            { x1: this.level.width, y1: 0, x2: this.level.width, y2: this.level.height }, // Right
            { x1: this.level.width, y1: this.level.height, x2: 0, y2: this.level.height }, // Bottom
            { x1: 0, y1: this.level.height, x2: 0, y2: 0 } // Left
        ];

        // Add some interior walls for cover
        for (let i = 0; i < 8; i++) {
            const x = Math.random() * (this.level.width - 200) + 100;
            const y = Math.random() * (this.level.height - 200) + 100;
            const width = 80 + Math.random() * 120;
            const height = 20 + Math.random() * 40;

            this.level.walls.push(
                { x1: x, y1: y, x2: x + width, y2: y },
                { x1: x + width, y1: y, x2: x + width, y2: y + height },
                { x1: x + width, y1: y + height, x2: x, y2: y + height },
                { x1: x, y1: y + height, x2: x, y2: y }
            );
        }
    }
    
    setupEventListeners() {
        // Menu buttons
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });

        // Mouse controls
        document.addEventListener('mousemove', (e) => {
            if (this.gameStarted && this.pointerLocked) {
                this.mouse.x = e.movementX || 0;
                this.mouse.y = e.movementY || 0;
                this.player.angle += this.mouse.x * this.mouse.sensitivity;
            }
        });

        // Mouse buttons
        document.addEventListener('mousedown', (e) => {
            if (this.gameStarted) {
                if (e.button === 0) { // Left click
                    this.fire();
                } else if (e.button === 2) { // Right click
                    this.altFire();
                }
            }
        });

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;

            if (this.gameStarted) {
                // Weapon switching (1-9)
                if (e.code >= 'Digit1' && e.code <= 'Digit9') {
                    const weaponIndex = parseInt(e.code.slice(-1)) - 1;
                    if (weaponIndex < this.weapons.length && this.weaponInventory[weaponIndex]) {
                        this.switchWeapon(weaponIndex);
                    }
                }

                // Other controls
                switch (e.code) {
                    case 'KeyR':
                        this.reload();
                        break;
                    case 'KeyF':
                        this.interact();
                        break;
                    case 'KeyT':
                        this.sayQuote();
                        break;
                    case 'Tab':
                        e.preventDefault();
                        this.toggleMap();
                        break;
                    case 'Escape':
                        this.pauseGame();
                        break;
                }
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });

        // Pointer lock events
        document.addEventListener('pointerlockchange', () => {
            this.pointerLocked = document.pointerLockElement === this.canvas;
        });

        // Prevent context menu
        document.addEventListener('contextmenu', (e) => e.preventDefault());

        // Window focus/blur
        window.addEventListener('blur', () => {
            if (this.gameStarted) this.pauseGame();
        });
    }
    
    startGame() {
        console.log('Starting game...');

        // Hide menu screen
        const menuScreen = document.getElementById('menuScreen');
        if (menuScreen) {
            menuScreen.style.display = 'none';
        }

        // Hide loading screen if it exists
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }

        // Start game immediately
        this.gameState = 'playing';
        this.gameStarted = true;

        // Request pointer lock
        try {
            this.canvas.requestPointerLock();
        } catch (e) {
            console.log('Pointer lock not available, continuing without it');
        }

        // Welcome messages
        this.showMessage("WELCOME TO DUKE NUKEM 3D!", 3000);
        setTimeout(() => {
            this.sayQuote();
        }, 1500);

        console.log('Game started successfully!');
    }

    finishLoading() {
        // This method is no longer used - keeping for compatibility
        console.log('finishLoading called');
    }

    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            this.showMessage("GAME PAUSED - Press ESC to continue", -1);
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.hideMessage();
        }
    }

    sayQuote() {
        const quote = this.dukeQuotes[Math.floor(Math.random() * this.dukeQuotes.length)];
        this.showMessage(quote, 3000);
    }
    
    spawnEnemies() {
        const enemyCount = 5 + this.level * 2; // More enemies per level
        for (let i = 0; i < enemyCount; i++) {
            this.spawnSingleEnemy();
        }
    }

    spawnSingleEnemy() {
        // Choose enemy type based on level
        let enemyType;
        const rand = Math.random();
        if (this.level === 1) {
            enemyType = rand < 0.7 ? 'pigCop' : 'alien';
        } else if (this.level <= 3) {
            if (rand < 0.4) enemyType = 'pigCop';
            else if (rand < 0.7) enemyType = 'alien';
            else enemyType = 'octabrain';
        } else {
            if (rand < 0.3) enemyType = 'pigCop';
            else if (rand < 0.5) enemyType = 'alien';
            else if (rand < 0.7) enemyType = 'octabrain';
            else if (rand < 0.9) enemyType = 'enforcer';
            else enemyType = 'boss';
        }

        const template = this.enemyTypes[enemyType];
        let x, y, attempts = 0;
        let validPosition = false;

        // Find valid spawn position
        while (!validPosition && attempts < 50) {
            x = Math.random() * (this.level.width - 200) + 100;
            y = Math.random() * (this.level.height - 200) + 100;

            // Check distance from player
            const distFromPlayer = Math.hypot(this.player.x - x, this.player.y - y);
            if (distFromPlayer < 150) {
                attempts++;
                continue;
            }

            // Check distance from other enemies
            let tooClose = false;
            for (let enemy of this.enemies) {
                const distFromEnemy = Math.hypot(enemy.x - x, enemy.y - y);
                if (distFromEnemy < template.size * 2) {
                    tooClose = true;
                    break;
                }
            }

            if (!tooClose) {
                validPosition = true;
            }
            attempts++;
        }

        // Create enemy with enhanced properties
        const enemy = {
            type: enemyType,
            x: x || Math.random() * this.level.width,
            y: y || Math.random() * this.level.height,
            health: template.health,
            maxHealth: template.health,
            speed: template.speed,
            damage: template.damage,
            size: template.size,
            color: template.color,
            ai: template.ai,
            weapon: template.weapon,
            points: template.points,
            angle: Math.random() * Math.PI * 2,
            state: 'patrol', // patrol, alert, chase, attack, dead
            lastSeen: 0,
            attackCooldown: 0,
            pathfinding: [],
            stunned: false,
            frozen: false,
            shrunk: false
        };

        this.enemies.push(enemy);
    }
    
    spawnPickups() {
        // Spawn health packs
        for (let i = 0; i < 3; i++) {
            this.spawnPickup('health');
        }

        // Spawn armor
        for (let i = 0; i < 2; i++) {
            this.spawnPickup('armor');
        }

        // Spawn weapon pickups
        for (let i = 2; i < this.weapons.length; i++) {
            if (Math.random() < 0.6) { // 60% chance for each weapon
                this.spawnPickup('weapon', i);
            }
        }

        // Spawn ammo
        for (let i = 0; i < 8; i++) {
            this.spawnPickup('ammo');
        }
    }

    spawnPickup(type, weaponId = null) {
        let x, y, attempts = 0;
        let validPosition = false;

        while (!validPosition && attempts < 30) {
            x = Math.random() * (this.level.width - 100) + 50;
            y = Math.random() * (this.level.height - 100) + 50;

            // Check distance from player
            const distFromPlayer = Math.hypot(this.player.x - x, this.player.y - y);
            if (distFromPlayer < 80) {
                attempts++;
                continue;
            }

            // Check distance from other pickups
            let tooClose = false;
            for (let pickup of this.pickups) {
                const distFromPickup = Math.hypot(pickup.x - x, pickup.y - y);
                if (distFromPickup < 40) {
                    tooClose = true;
                    break;
                }
            }

            if (!tooClose) {
                validPosition = true;
            }
            attempts++;
        }

        const template = this.pickupTypes[type];
        const pickup = {
            type: type,
            x: x || Math.random() * this.level.width,
            y: y || Math.random() * this.level.height,
            size: template.size,
            color: template.color,
            rotation: 0,
            glowPhase: 0,
            bobPhase: Math.random() * Math.PI * 2,
            weaponId: weaponId,
            value: template.value
        };

        this.pickups.push(pickup);
    }
    
    update() {
        if (!this.gameStarted || this.gameState !== 'playing') return;

        this.updatePlayer();
        this.updateEnemies();
        this.updatePickups();
        this.updateEffects();
        this.updateWeaponBobbing();
        this.checkCollisions();
        this.updateEnemyRespawn();
        this.checkWinCondition();
    }

    updateEnemyRespawn() {
        // Respawn enemies if below maximum and timer is ready
        if (this.enemies.length < this.maxEnemies) {
            this.enemyRespawnTimer++;

            if (this.enemyRespawnTimer >= this.enemyRespawnDelay / 16) { // Convert to frames (assuming 60fps)
                this.spawnSingleEnemy();
                this.enemyRespawnTimer = 0;

                // Show message occasionally
                if (Math.random() < 0.3) {
                    this.showMessage("MORE ENEMIES INCOMING!", 2000);
                }
            }
        }
    }

    checkWinCondition() {
        // Check if enough enemies have been killed to advance level
        const killsNeeded = 10 + (this.level * 5);
        if (this.enemiesKilled >= killsNeeded) {
            this.level++;
            this.enemiesKilled = 0;
            this.maxEnemies = Math.min(20, 8 + this.level * 2); // Increase max enemies per level
            this.showMessage(`LEVEL ${this.level} COMPLETE!`, 3000);
            this.showMessage(`KILLS NEEDED: ${10 + (this.level * 5)}`, 2000);
            setTimeout(() => {
                this.sayQuote();
            }, 1500);
        }
    }
    
    updatePlayer() {
        // Handle movement states
        this.player.isRunning = this.keys['ShiftLeft'] || this.keys['ShiftRight'];
        this.player.isCrouching = this.keys['ControlLeft'] || this.keys['ControlRight'];

        // Determine movement speed
        let speed = this.player.speed;
        if (this.player.isRunning && !this.player.isCrouching) {
            speed = this.player.runSpeed;
        } else if (this.player.isCrouching) {
            speed = this.player.crouchSpeed;
        }

        // Handle jumping
        if (this.keys['Space'] && this.player.onGround && !this.player.isCrouching) {
            this.player.jumpVelocity = this.player.jumpSpeed;
            this.player.onGround = false;
            this.player.isJumping = true;
        }

        // Update vertical position (jumping/falling)
        if (!this.player.onGround) {
            this.player.z += this.player.jumpVelocity;
            this.player.jumpVelocity -= 0.5; // Gravity

            if (this.player.z <= 0) {
                this.player.z = 0;
                this.player.onGround = true;
                this.player.isJumping = false;
                this.player.jumpVelocity = 0;
            }
        }

        // Calculate movement
        let dx = 0, dy = 0;

        if (this.keys['KeyW']) {
            dx += Math.cos(this.player.angle) * speed;
            dy += Math.sin(this.player.angle) * speed;
        }
        if (this.keys['KeyS']) {
            dx -= Math.cos(this.player.angle) * speed;
            dy -= Math.sin(this.player.angle) * speed;
        }
        if (this.keys['KeyA']) {
            dx += Math.cos(this.player.angle - Math.PI/2) * speed;
            dy += Math.sin(this.player.angle - Math.PI/2) * speed;
        }
        if (this.keys['KeyD']) {
            dx += Math.cos(this.player.angle + Math.PI/2) * speed;
            dy += Math.sin(this.player.angle + Math.PI/2) * speed;
        }

        // Apply movement with collision detection
        const newX = this.player.x + dx;
        const newY = this.player.y + dy;

        // Check wall collisions
        if (this.isValidPosition(newX, this.player.y, 15)) {
            this.player.x = newX;
        }
        if (this.isValidPosition(this.player.x, newY, 15)) {
            this.player.y = newY;
        }

        // Boundary checking
        this.player.x = Math.max(20, Math.min(this.level.width - 20, this.player.x));
        this.player.y = Math.max(20, Math.min(this.level.height - 20, this.player.y));
    }

    isValidPosition(x, y, radius) {
        // Check against level walls
        for (let wall of this.level.walls) {
            const dist = this.distanceToLineSegment(x, y, wall.x1, wall.y1, wall.x2, wall.y2);
            if (dist < radius) {
                return false;
            }
        }
        return true;
    }

    distanceToLineSegment(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.hypot(A, B);

        const param = dot / lenSq;

        let xx, yy;
        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        return Math.hypot(px - xx, py - yy);
    }
    
    updateEnemies() {
        this.enemies.forEach((enemy, index) => {
            if (enemy.health <= 0) {
                if (enemy.state !== 'dead') {
                    enemy.state = 'dead';
                    this.score += enemy.points;
                    this.kills++;
                    this.enemiesKilled++;
                    this.createBloodEffect(enemy.x, enemy.y);
                    this.sayRandomKillQuote();
                }
                // Remove dead enemies after a delay
                setTimeout(() => {
                    const deadIndex = this.enemies.indexOf(enemy);
                    if (deadIndex > -1) {
                        this.enemies.splice(deadIndex, 1);
                    }
                }, 2000);
                return;
            }

            // Skip if stunned or frozen
            if (enemy.stunned || enemy.frozen) {
                if (enemy.stunned) enemy.stunned--;
                if (enemy.frozen) enemy.frozen--;
                return;
            }

            const distToPlayer = Math.hypot(
                this.player.x - enemy.x,
                this.player.y - enemy.y
            );

            // AI behavior based on enemy type and distance
            this.updateEnemyAI(enemy, distToPlayer);

            // Update attack cooldown
            if (enemy.attackCooldown > 0) {
                enemy.attackCooldown--;
            }
        });
    }

    updateEnemyAI(enemy, distToPlayer) {
        const sightRange = 200;
        const attackRange = 50;

        switch (enemy.ai) {
            case 'aggressive':
                if (distToPlayer < sightRange) {
                    enemy.state = 'chase';
                    this.moveEnemyTowardsPlayer(enemy);
                    if (distToPlayer < attackRange && enemy.attackCooldown === 0) {
                        this.enemyAttack(enemy);
                    }
                } else {
                    this.enemyPatrol(enemy);
                }
                break;

            case 'flying':
                // Flying enemies move more erratically
                if (distToPlayer < sightRange) {
                    enemy.state = 'chase';
                    const angleToPlayer = Math.atan2(
                        this.player.y - enemy.y,
                        this.player.x - enemy.x
                    );
                    // Add some randomness to flight pattern
                    const randomOffset = (Math.random() - 0.5) * 0.5;
                    enemy.x += Math.cos(angleToPlayer + randomOffset) * enemy.speed;
                    enemy.y += Math.sin(angleToPlayer + randomOffset) * enemy.speed;

                    if (distToPlayer < attackRange * 2 && enemy.attackCooldown === 0) {
                        this.enemyAttack(enemy);
                    }
                } else {
                    this.enemyPatrol(enemy);
                }
                break;

            case 'tank':
                // Tank enemies are slower but more persistent
                if (distToPlayer < sightRange * 1.5) {
                    enemy.state = 'chase';
                    this.moveEnemyTowardsPlayer(enemy);
                    if (distToPlayer < attackRange * 1.5 && enemy.attackCooldown === 0) {
                        this.enemyAttack(enemy);
                    }
                } else {
                    this.enemyPatrol(enemy);
                }
                break;

            case 'boss':
                // Boss always knows where player is
                enemy.state = 'chase';
                this.moveEnemyTowardsPlayer(enemy);
                if (enemy.attackCooldown === 0) {
                    this.enemyAttack(enemy);
                }
                break;

            default:
                this.enemyPatrol(enemy);
        }

        // Boundary checking
        if (enemy.x < enemy.size || enemy.x > this.level.width - enemy.size ||
            enemy.y < enemy.size || enemy.y > this.level.height - enemy.size) {
            enemy.angle = Math.random() * Math.PI * 2;
        }
    }

    moveEnemyTowardsPlayer(enemy) {
        const angleToPlayer = Math.atan2(
            this.player.y - enemy.y,
            this.player.x - enemy.x
        );

        const newX = enemy.x + Math.cos(angleToPlayer) * enemy.speed;
        const newY = enemy.y + Math.sin(angleToPlayer) * enemy.speed;

        // Check for wall collisions
        if (this.isValidPosition(newX, enemy.y, enemy.size)) {
            enemy.x = newX;
        }
        if (this.isValidPosition(enemy.x, newY, enemy.size)) {
            enemy.y = newY;
        }

        enemy.angle = angleToPlayer;
    }

    enemyPatrol(enemy) {
        enemy.state = 'patrol';
        const newX = enemy.x + Math.cos(enemy.angle) * enemy.speed * 0.5;
        const newY = enemy.y + Math.sin(enemy.angle) * enemy.speed * 0.5;

        if (this.isValidPosition(newX, enemy.y, enemy.size)) {
            enemy.x = newX;
        } else {
            enemy.angle = Math.random() * Math.PI * 2;
        }

        if (this.isValidPosition(enemy.x, newY, enemy.size)) {
            enemy.y = newY;
        } else {
            enemy.angle = Math.random() * Math.PI * 2;
        }

        // Random direction changes
        if (Math.random() < 0.02) {
            enemy.angle = Math.random() * Math.PI * 2;
        }
    }

    enemyAttack(enemy) {
        enemy.attackCooldown = 60; // 1 second at 60fps

        // Create attack effect
        this.createMuzzleFlash(enemy.x, enemy.y, enemy.color);

        // Check if attack hits player
        const distToPlayer = Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y);
        const attackRange = enemy.type === 'boss' ? 100 : 50;

        if (distToPlayer < attackRange) {
            this.playerTakeDamage(enemy.damage);
        }
    }

    sayRandomKillQuote() {
        const killQuotes = [
            "Damn, I'm good!",
            "Groovy!",
            "Let God sort 'em out!",
            "Die, you son of a bitch!",
            "Eat shit and die!"
        ];

        if (Math.random() < 0.3) { // 30% chance
            const quote = killQuotes[Math.floor(Math.random() * killQuotes.length)];
            this.showMessage(quote, 2000);
        }
    }
    
    updatePickups() {
        this.pickups.forEach(pickup => {
            pickup.rotation += 0.03;
            pickup.glowPhase += 0.08;
            pickup.bobPhase += 0.1;
        });
    }

    updateEffects() {
        // Update muzzle flashes
        this.effects.muzzleFlashes = this.effects.muzzleFlashes.filter(flash => {
            flash.life -= 16;
            return flash.life > 0;
        });

        // Update impacts
        this.effects.impacts = this.effects.impacts.filter(impact => {
            impact.life -= 16;
            impact.size *= 0.95;
            return impact.life > 0;
        });

        // Update blood effects
        this.effects.blood = this.effects.blood.filter(blood => {
            blood.x += blood.vx;
            blood.y += blood.vy;
            blood.vx *= 0.98;
            blood.vy *= 0.98;
            blood.life -= 8;
            return blood.life > 0;
        });
    }

    updateWeaponBobbing() {
        // Create weapon bobbing effect when moving
        const isMoving = this.keys['KeyW'] || this.keys['KeyS'] || this.keys['KeyA'] || this.keys['KeyD'];
        if (isMoving) {
            this.weaponBobbing += 0.3;
        } else {
            this.weaponBobbing *= 0.9;
        }

        // Reduce recoil
        if (this.weaponRecoil > 0) {
            this.weaponRecoil *= 0.8;
        }
    }

    checkCollisions() {
        // Check enemy collisions with player
        this.enemies.forEach(enemy => {
            if (enemy.state === 'dead') return;

            const dist = Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y);
            if (dist < enemy.size + 15) {
                this.playerTakeDamage(enemy.damage * 0.1); // Continuous damage
            }
        });

        // Check pickup proximity for prompts
        this.pickups.forEach(pickup => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 60) {
                this.showPickupPrompt(pickup);
            }
        });
    }

    playerTakeDamage(amount) {
        // Armor absorbs some damage
        let actualDamage = amount;
        if (this.player.armor > 0) {
            const armorAbsorbed = Math.min(this.player.armor, amount * 0.5);
            this.player.armor -= armorAbsorbed;
            actualDamage -= armorAbsorbed;
        }

        this.player.health = Math.max(0, this.player.health - actualDamage);

        // Damage flash effect
        const overlay = document.getElementById('damageOverlay');
        overlay.style.background = 'rgba(255, 0, 0, 0.4)';
        setTimeout(() => {
            overlay.style.background = 'rgba(255, 0, 0, 0)';
        }, 200);

        this.updateUI();

        if (this.player.health <= 0) {
            this.gameOver();
        }
    }

    gameOver() {
        this.gameState = 'gameOver';
        this.gameStarted = false;
        this.showMessage(`GAME OVER - SCORE: ${this.score} - Press F5 to restart`, 10000);
    }
    
    updateEffects() {
        // Update muzzle flashes
        this.muzzleFlashes = this.muzzleFlashes.filter(flash => {
            flash.life -= 16;
            return flash.life > 0;
        });
        
        // Update impact effects
        this.impacts = this.impacts.filter(impact => {
            impact.life -= 16;
            impact.size *= 0.95;
            return impact.life > 0;
        });
        
        // Update particles
        this.particles = this.particles.filter(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= 16;
            particle.size *= 0.98;
            return particle.life > 0;
        });
    }
    
    checkCollisions() {
        // Check enemy collisions with player
        this.enemies.forEach(enemy => {
            const dist = Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y);
            if (dist < 30) {
                this.takeDamage(10);
            }
        });
        
        // Check pickup collisions
        this.pickups.forEach((pickup, index) => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 40) {
                this.showPickupPrompt(pickup);
            }
        });
    }
    
    fire() {
        const weapon = this.weapons[this.currentWeapon];
        const now = Date.now();

        // Check if can fire
        if (now - this.lastFireTime < weapon.fireRate ||
            (weapon.ammo !== -1 && weapon.ammo <= 0) ||
            this.reloading) {
            return;
        }

        this.lastFireTime = now;

        // Consume ammo (except for melee weapons)
        if (weapon.ammo !== -1) {
            weapon.ammo--;
        }

        // Create muzzle flash
        this.createMuzzleFlash(
            this.player.x + Math.cos(this.player.angle) * 40,
            this.player.y + Math.sin(this.player.angle) * 40,
            weapon.sprite
        );

        // Weapon-specific firing logic
        switch (weapon.type) {
            case 'melee':
                this.meleeAttack(weapon);
                break;
            case 'hitscan':
                this.hitscanAttack(weapon);
                break;
            case 'projectile':
                this.projectileAttack(weapon);
                break;
            case 'explosive':
                this.explosiveAttack(weapon);
                break;
            case 'special':
                this.specialAttack(weapon);
                break;
        }

        // Weapon recoil and screen shake
        this.weaponRecoil = 10;
        this.screenShake(weapon.damage / 20);

        this.updateUI();
    }

    meleeAttack(weapon) {
        // Check for enemies in melee range
        this.enemies.forEach(enemy => {
            const dist = Math.hypot(this.player.x - enemy.x, this.player.y - enemy.y);
            if (dist < weapon.range) {
                const angleToEnemy = Math.atan2(enemy.y - this.player.y, enemy.x - this.player.x);
                const angleDiff = Math.abs(this.player.angle - angleToEnemy);

                // Check if enemy is in front of player
                if (angleDiff < Math.PI / 3) {
                    enemy.health -= weapon.damage;
                    this.createBloodEffect(enemy.x, enemy.y);
                    this.showMessage("MIGHTY FOOT!", 1000);
                }
            }
        });
    }

    hitscanAttack(weapon) {
        const pellets = weapon.pellets || 1;
        const spread = weapon.spread || 0.1;

        for (let i = 0; i < pellets; i++) {
            const angle = this.player.angle + (Math.random() - 0.5) * spread;
            this.castRay(angle, weapon.range, weapon.damage / pellets);
        }
    }

    projectileAttack(weapon) {
        // Create projectile
        this.createProjectile(
            this.player.x + Math.cos(this.player.angle) * 40,
            this.player.y + Math.sin(this.player.angle) * 40,
            this.player.angle,
            weapon
        );
    }

    explosiveAttack(weapon) {
        // Throw explosive (pipe bomb)
        this.createExplosive(
            this.player.x + Math.cos(this.player.angle) * 40,
            this.player.y + Math.sin(this.player.angle) * 40,
            this.player.angle,
            weapon
        );
    }

    specialAttack(weapon) {
        // Special weapons like shrinker, freezer
        const angle = this.player.angle;
        this.castSpecialRay(angle, weapon.range, weapon);
    }
    
    castRay(angle, range, damage) {
        const endX = this.player.x + Math.cos(angle) * range;
        const endY = this.player.y + Math.sin(angle) * range;

        // Check enemy hits
        this.enemies.forEach(enemy => {
            const dist = this.distanceToLineSegment(
                enemy.x, enemy.y,
                this.player.x, this.player.y,
                endX, endY
            );

            if (dist < enemy.size) {
                enemy.health -= damage;
                this.createBloodEffect(enemy.x, enemy.y);
                this.createImpactEffect(enemy.x, enemy.y);
            }
        });
    }

    createMuzzleFlash(x, y, weaponType) {
        this.effects.muzzleFlashes.push({
            x: x,
            y: y,
            life: 100,
            maxLife: 100,
            size: 20,
            color: this.getMuzzleFlashColor(weaponType)
        });
    }

    getMuzzleFlashColor(weaponType) {
        const colors = {
            pistol: '#ffff00',
            shotgun: '#ff6600',
            chaingun: '#ff9900',
            rpg: '#ff0000',
            devastator: '#ff0066'
        };
        return colors[weaponType] || '#ffffff';
    }

    createBloodEffect(x, y) {
        for (let i = 0; i < 8; i++) {
            this.effects.blood.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 6,
                vy: (Math.random() - 0.5) * 6,
                life: 300,
                maxLife: 300,
                size: 2 + Math.random() * 3,
                color: '#cc0000'
            });
        }
    }

    createImpactEffect(x, y) {
        this.effects.impacts.push({
            x: x,
            y: y,
            life: 200,
            maxLife: 200,
            size: 15,
            color: '#ffff00'
        });
    }

    switchWeapon(index) {
        if (index >= 0 && index < this.weapons.length && this.weaponInventory[index]) {
            this.currentWeapon = index;
            this.updateUI();
            const weapon = this.weapons[index];
            this.showMessage(`${weapon.name} SELECTED`, 1500);
        }
    }

    interact() {
        // Check for nearby pickups
        this.pickups.forEach((pickup, index) => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 50) {
                this.collectPickup(pickup, index);
            }
        });
    }

    collectPickup(pickup, index) {
        switch (pickup.type) {
            case 'health':
                if (this.player.health < this.player.maxHealth) {
                    this.player.health = Math.min(this.player.maxHealth, this.player.health + pickup.value);
                    this.showMessage(`+${pickup.value} HEALTH`, 1500);
                    this.pickups.splice(index, 1);
                }
                break;

            case 'armor':
                if (this.player.armor < this.player.maxArmor) {
                    this.player.armor = Math.min(this.player.maxArmor, this.player.armor + pickup.value);
                    this.showMessage(`+${pickup.value} ARMOR`, 1500);
                    this.pickups.splice(index, 1);
                }
                break;

            case 'weapon':
                if (pickup.weaponId && !this.weaponInventory[pickup.weaponId]) {
                    this.weaponInventory[pickup.weaponId] = true;
                    const weapon = this.weapons[pickup.weaponId];
                    this.showMessage(`${weapon.name} ACQUIRED!`, 2000);
                    this.pickups.splice(index, 1);
                }
                break;

            case 'ammo':
                const currentWeapon = this.weapons[this.currentWeapon];
                if (currentWeapon.ammo < currentWeapon.maxAmmo) {
                    currentWeapon.ammo = Math.min(currentWeapon.maxAmmo, currentWeapon.ammo + 10);
                    this.showMessage("+10 AMMO", 1000);
                    this.pickups.splice(index, 1);
                }
                break;
        }
        this.updateUI();
    }
    
    distanceToLine(x1, y1, x2, y2, px, py) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        
        if (lenSq === 0) return Math.hypot(A, B);
        
        const param = dot / lenSq;
        
        let xx, yy;
        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }
        
        return Math.hypot(px - xx, py - yy);
    }
    
    reload() {
        const weapon = this.weapons[this.currentWeapon];
        if (weapon.ammo === weapon.maxAmmo || this.reloading) return;
        
        this.reloading = true;
        document.getElementById('reloadText').classList.remove('hidden');
        
        setTimeout(() => {
            weapon.ammo = weapon.maxAmmo;
            this.reloading = false;
            document.getElementById('reloadText').classList.add('hidden');
            this.updateUI();
            this.showMessage('Reloaded!', 1000);
        }, 2000);
    }
    
    switchWeapon(index) {
        if (index >= 0 && index < this.weapons.length) {
            this.currentWeapon = index;
            this.updateUI();
            this.showMessage(`Switched to ${this.weapons[index].name}`, 1500);
        }
    }
    
    tryPickup() {
        this.pickups.forEach((pickup, index) => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist < 40) {
                const weapon = this.weapons[pickup.weapon];
                weapon.ammo = weapon.maxAmmo;
                this.pickups.splice(index, 1);
                this.showMessage(`Picked up ${weapon.name}!`, 2000);
                this.updateUI();
            }
        });
    }
    
    takeDamage(amount) {
        this.player.health = Math.max(0, this.player.health - amount);
        
        // Damage flash effect
        const overlay = document.getElementById('damageOverlay');
        overlay.style.background = 'rgba(255, 0, 0, 0.3)';
        setTimeout(() => {
            overlay.style.background = 'rgba(255, 0, 0, 0)';
        }, 200);
        
        this.updateUI();
        
        if (this.player.health <= 0) {
            this.gameOver();
        }
    }
    
    screenShake() {
        const intensity = 5;
        const duration = 100;
        
        this.canvas.style.transform = `translate(${(Math.random() - 0.5) * intensity}px, ${(Math.random() - 0.5) * intensity}px)`;
        
        setTimeout(() => {
            this.canvas.style.transform = 'translate(0, 0)';
        }, duration);
    }
    
    showMessage(text, duration) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.textContent = text;
        messagesDiv.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, duration);
    }
    
    showPickupPrompt(pickup) {
        // Remove any existing pickup prompts
        const existingPrompts = document.querySelectorAll('.pickup-prompt');
        existingPrompts.forEach(prompt => prompt.remove());

        // Create new pickup prompt
        const prompt = document.createElement('div');
        prompt.className = 'pickup-prompt';
        prompt.textContent = `Press F to pick up ${this.weapons[pickup.weapon].name}`;

        // Position the prompt near the pickup
        const rect = this.canvas.getBoundingClientRect();
        const screenX = (pickup.x / this.canvas.width) * rect.width + rect.left;
        const screenY = (pickup.y / this.canvas.height) * rect.height + rect.top - 30;

        prompt.style.left = screenX + 'px';
        prompt.style.top = screenY + 'px';

        document.body.appendChild(prompt);

        // Remove prompt after a short delay if player moves away
        setTimeout(() => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist >= 40) {
                prompt.remove();
            }
        }, 100);
    }
    
    updateUI() {
        const weapon = this.weapons[this.currentWeapon];

        // Update health bar
        const healthPercent = (this.player.health / this.player.maxHealth) * 100;
        document.getElementById('healthFill').style.width = healthPercent + '%';
        document.getElementById('healthValue').textContent = Math.floor(this.player.health);

        // Update armor bar
        const armorPercent = (this.player.armor / this.player.maxArmor) * 100;
        document.getElementById('armorFill').style.width = armorPercent + '%';
        document.getElementById('armorValue').textContent = Math.floor(this.player.armor);

        // Update weapon info
        document.getElementById('currentWeaponName').textContent = weapon.name;
        document.getElementById('ammoCount').textContent = weapon.ammo === -1 ? '∞' : weapon.ammo;
        document.getElementById('maxAmmoCount').textContent = weapon.maxAmmo === -1 ? '∞' : weapon.maxAmmo;

        // Update score
        document.getElementById('scoreValue').textContent = this.score;
    }
    
    gameOver() {
        this.gameStarted = false;
        this.showMessage('GAME OVER - Press F5 to restart', 10000);
    }
    
    render() {
        // Clear canvas
        this.ctx.fillStyle = '#1a1a1a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw level background
        this.drawLevel();

        // Draw pickups
        this.pickups.forEach(pickup => this.drawPickup(pickup));

        // Draw enemies
        this.enemies.forEach(enemy => this.drawEnemy(enemy));

        // Draw effects
        this.drawEffects();

        // Draw player indicator (optional debug)
        if (false) {
            this.ctx.fillStyle = '#00ff00';
            this.ctx.beginPath();
            this.ctx.arc(this.player.x, this.player.y, 8, 0, Math.PI * 2);
            this.ctx.fill();

            // Draw player direction
            this.ctx.strokeStyle = '#00ff00';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.moveTo(this.player.x, this.player.y);
            this.ctx.lineTo(
                this.player.x + Math.cos(this.player.angle) * 30,
                this.player.y + Math.sin(this.player.angle) * 30
            );
            this.ctx.stroke();
        }
    }

    drawLevel() {
        // Draw floor pattern
        this.ctx.fillStyle = '#2a2a2a';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw grid pattern
        this.ctx.strokeStyle = 'rgba(255, 255, 0, 0.1)';
        this.ctx.lineWidth = 1;

        const gridSize = 50;
        for (let x = 0; x < this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y < this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }

        // Draw walls
        this.ctx.strokeStyle = '#666666';
        this.ctx.lineWidth = 3;
        this.level.walls.forEach(wall => {
            this.ctx.beginPath();
            this.ctx.moveTo(wall.x1, wall.y1);
            this.ctx.lineTo(wall.x2, wall.y2);
            this.ctx.stroke();
        });
    }
    
    drawRetroBackground() {
        // Draw grid pattern for retro feel
        this.ctx.strokeStyle = 'rgba(255, 153, 51, 0.1)';
        this.ctx.lineWidth = 1;
        
        const gridSize = 50;
        for (let x = 0; x < this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y < this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
    
    drawEnemy(enemy) {
        if (enemy.state === 'dead') return;

        this.ctx.save();
        this.ctx.translate(enemy.x, enemy.y);

        // Enemy body with type-specific appearance
        this.ctx.fillStyle = enemy.color;

        // Different shapes for different enemy types
        switch (enemy.type) {
            case 'pigCop':
                this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
                break;
            case 'alien':
                this.ctx.beginPath();
                this.ctx.arc(0, 0, enemy.size/2, 0, Math.PI * 2);
                this.ctx.fill();
                break;
            case 'octabrain':
                // Draw octagonal shape
                this.ctx.beginPath();
                for (let i = 0; i < 8; i++) {
                    const angle = (i / 8) * Math.PI * 2;
                    const x = Math.cos(angle) * enemy.size/2;
                    const y = Math.sin(angle) * enemy.size/2;
                    if (i === 0) this.ctx.moveTo(x, y);
                    else this.ctx.lineTo(x, y);
                }
                this.ctx.closePath();
                this.ctx.fill();
                break;
            case 'enforcer':
                this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
                // Add armor plating effect
                this.ctx.strokeStyle = '#ffffff';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
                break;
            case 'boss':
                this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
                // Add boss glow effect
                this.ctx.shadowColor = enemy.color;
                this.ctx.shadowBlur = 20;
                this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
                break;
            default:
                this.ctx.fillRect(-enemy.size/2, -enemy.size/2, enemy.size, enemy.size);
        }

        // Health bar
        const healthPercent = enemy.health / enemy.maxHealth;
        this.ctx.shadowBlur = 0;
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2 - 12, enemy.size, 6);
        this.ctx.fillStyle = healthPercent > 0.6 ? '#00ff00' : healthPercent > 0.3 ? '#ffff00' : '#ff0000';
        this.ctx.fillRect(-enemy.size/2, -enemy.size/2 - 12, enemy.size * healthPercent, 6);

        // Direction indicator
        if (enemy.state === 'chase') {
            this.ctx.strokeStyle = '#ff0000';
            this.ctx.lineWidth = 2;
            this.ctx.beginPath();
            this.ctx.moveTo(0, 0);
            this.ctx.lineTo(Math.cos(enemy.angle) * enemy.size, Math.sin(enemy.angle) * enemy.size);
            this.ctx.stroke();
        }

        this.ctx.restore();
    }
    
    drawPickup(pickup) {
        this.ctx.save();
        this.ctx.translate(pickup.x, pickup.y + Math.sin(pickup.bobPhase) * 3);
        this.ctx.rotate(pickup.rotation);

        // Glow effect
        const glowIntensity = 0.7 + Math.sin(pickup.glowPhase) * 0.3;
        this.ctx.shadowColor = pickup.color;
        this.ctx.shadowBlur = 15 * glowIntensity;

        // Draw pickup based on type
        this.ctx.fillStyle = pickup.color;

        switch (pickup.type) {
            case 'health':
                // Draw cross shape
                this.ctx.fillRect(-pickup.size/2, -pickup.size/6, pickup.size, pickup.size/3);
                this.ctx.fillRect(-pickup.size/6, -pickup.size/2, pickup.size/3, pickup.size);
                break;

            case 'armor':
                // Draw shield shape
                this.ctx.beginPath();
                this.ctx.arc(0, -pickup.size/4, pickup.size/2, 0, Math.PI, true);
                this.ctx.lineTo(-pickup.size/2, pickup.size/2);
                this.ctx.lineTo(0, pickup.size/2 + pickup.size/4);
                this.ctx.lineTo(pickup.size/2, pickup.size/2);
                this.ctx.closePath();
                this.ctx.fill();
                break;

            case 'ammo':
                // Draw bullet shape
                this.ctx.fillRect(-pickup.size/4, -pickup.size/2, pickup.size/2, pickup.size);
                this.ctx.beginPath();
                this.ctx.arc(0, -pickup.size/2, pickup.size/4, 0, Math.PI * 2);
                this.ctx.fill();
                break;

            case 'weapon':
                // Draw weapon icon
                this.ctx.fillRect(-pickup.size/2, -pickup.size/4, pickup.size, pickup.size/2);
                this.ctx.fillRect(-pickup.size/4, -pickup.size/2, pickup.size/2, pickup.size/4);

                // Draw weapon number if applicable
                if (pickup.weaponId) {
                    this.ctx.shadowBlur = 0;
                    this.ctx.fillStyle = '#ffffff';
                    this.ctx.font = 'bold 12px Courier New';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText((pickup.weaponId + 1).toString(), 0, 4);
                }
                break;

            default:
                this.ctx.fillRect(-pickup.size/2, -pickup.size/2, pickup.size, pickup.size);
        }

        this.ctx.restore();
    }
    
    drawEffects() {
        // Draw muzzle flashes
        this.effects.muzzleFlashes.forEach(flash => {
            this.ctx.save();
            this.ctx.globalAlpha = flash.life / flash.maxLife;
            this.ctx.fillStyle = flash.color;
            this.ctx.shadowColor = flash.color;
            this.ctx.shadowBlur = 25;

            this.ctx.beginPath();
            this.ctx.arc(flash.x, flash.y, flash.size, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();
        });

        // Draw impacts
        this.effects.impacts.forEach(impact => {
            this.ctx.save();
            this.ctx.globalAlpha = impact.life / impact.maxLife;
            this.ctx.fillStyle = impact.color;
            this.ctx.shadowColor = impact.color;
            this.ctx.shadowBlur = 15;

            this.ctx.beginPath();
            this.ctx.arc(impact.x, impact.y, impact.size, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();
        });

        // Draw blood effects
        this.effects.blood.forEach(blood => {
            this.ctx.save();
            this.ctx.globalAlpha = blood.life / blood.maxLife;
            this.ctx.fillStyle = blood.color;

            this.ctx.beginPath();
            this.ctx.arc(blood.x, blood.y, blood.size, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();
        });
    }

    showMessage(text, duration) {
        const messagesDiv = document.getElementById('messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.textContent = text;
        messagesDiv.appendChild(messageDiv);

        if (duration > 0) {
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, duration);
        }
    }

    hideMessage() {
        const messagesDiv = document.getElementById('messages');
        messagesDiv.innerHTML = '';
    }

    showPickupPrompt(pickup) {
        // Remove existing prompts
        const existingPrompts = document.querySelectorAll('.pickup-prompt');
        existingPrompts.forEach(prompt => prompt.remove());

        // Create new prompt
        const prompt = document.createElement('div');
        prompt.className = 'pickup-prompt';

        let promptText = '';
        switch (pickup.type) {
            case 'health':
                promptText = `Press F to pick up HEALTH PACK (+${pickup.value})`;
                break;
            case 'armor':
                promptText = `Press F to pick up ARMOR (+${pickup.value})`;
                break;
            case 'weapon':
                if (pickup.weaponId) {
                    promptText = `Press F to pick up ${this.weapons[pickup.weaponId].name}`;
                }
                break;
            case 'ammo':
                promptText = 'Press F to pick up AMMO';
                break;
            default:
                promptText = 'Press F to pick up';
        }

        prompt.textContent = promptText;

        // Position the prompt
        const rect = this.canvas.getBoundingClientRect();
        const screenX = (pickup.x / this.canvas.width) * rect.width + rect.left;
        const screenY = (pickup.y / this.canvas.height) * rect.height + rect.top - 40;

        prompt.style.left = screenX + 'px';
        prompt.style.top = screenY + 'px';

        document.body.appendChild(prompt);

        // Auto-remove after delay
        setTimeout(() => {
            const dist = Math.hypot(this.player.x - pickup.x, this.player.y - pickup.y);
            if (dist >= 60) {
                prompt.remove();
            }
        }, 100);
    }

    screenShake(intensity = 5) {
        const duration = 100;
        this.canvas.style.transform = `translate(${(Math.random() - 0.5) * intensity}px, ${(Math.random() - 0.5) * intensity}px)`;

        setTimeout(() => {
            this.canvas.style.transform = 'translate(0, 0)';
        }, duration);
    }
    
    gameLoop() {
        this.update();
        this.render();
        requestAnimationFrame(() => this.gameLoop());
    }

    // Additional methods for completeness
    altFire() {
        // Alternate fire mode for some weapons
        const weapon = this.weapons[this.currentWeapon];
        if (weapon.name === 'PIPE BOMBS') {
            // Detonate existing pipe bombs
            this.detonatePipeBombs();
        }
    }

    reload() {
        const weapon = this.weapons[this.currentWeapon];
        if (weapon.reloadTime && weapon.ammo < weapon.maxAmmo && !this.reloading) {
            this.reloading = true;
            this.showMessage('RELOADING...', weapon.reloadTime);

            setTimeout(() => {
                weapon.ammo = weapon.maxAmmo;
                this.reloading = false;
                this.updateUI();
                this.showMessage('LOCKED AND LOADED!', 1000);
            }, weapon.reloadTime);
        }
    }

    toggleMap() {
        // Toggle minimap (placeholder)
        this.showMessage('MAP FEATURE COMING SOON!', 2000);
    }

    // Placeholder methods for projectiles and explosives
    createProjectile(x, y, angle, weapon) {
        // Placeholder for RPG/Devastator projectiles
        this.showMessage(`${weapon.name} FIRED!`, 1000);
    }

    createExplosive(x, y, angle, weapon) {
        // Placeholder for pipe bombs
        this.showMessage('PIPE BOMB THROWN!', 1000);
    }

    castSpecialRay(angle, range, weapon) {
        // Placeholder for special weapons like shrinker/freezer
        this.showMessage(`${weapon.name} ACTIVATED!`, 1000);
    }

    detonatePipeBombs() {
        this.showMessage('BOOM!', 1500);
    }
}

// Initialize the game when page loads
window.addEventListener('load', () => {
    new DukeNukem3D();
});
