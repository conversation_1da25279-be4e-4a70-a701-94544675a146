<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duke Nukem 3D - Web Edition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            font-family: 'Courier New', monospace;
            overflow: hidden;
            cursor: none;
            user-select: none;
        }

        #gameCanvas {
            display: block;
            background: #000;
            image-rendering: pixelated;
            image-rendering: -moz-crisp-edges;
            image-rendering: crisp-edges;
            image-rendering: -webkit-crisp-edges;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        /* Duke Nukem HUD Styling */
        #statusBar {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 80px;
            background: linear-gradient(to bottom, #2a2a2a 0%, #1a1a1a 100%);
            border-top: 2px solid #666;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #fff;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }

        #healthSection {
            display: flex;
            align-items: center;
            margin-right: 30px;
        }

        #healthBar {
            width: 100px;
            height: 20px;
            background: #333;
            border: 2px solid #666;
            margin-left: 10px;
            position: relative;
        }

        #healthFill {
            height: 100%;
            background: linear-gradient(to right, #ff0000 0%, #ffff00 50%, #00ff00 100%);
            transition: width 0.3s ease;
        }

        #armorSection {
            display: flex;
            align-items: center;
            margin-right: 30px;
        }

        #armorBar {
            width: 100px;
            height: 20px;
            background: #333;
            border: 2px solid #666;
            margin-left: 10px;
            position: relative;
        }

        #armorFill {
            height: 100%;
            background: linear-gradient(to right, #0066ff 0%, #00ccff 100%);
            transition: width 0.3s ease;
        }

        #weaponDisplay {
            display: flex;
            align-items: center;
            margin-right: 30px;
        }

        #ammoDisplay {
            font-size: 18px;
            color: #ffff00;
        }

        #scoreDisplay {
            margin-left: auto;
            font-size: 16px;
            color: #00ff00;
        }

        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 24px;
            height: 24px;
            pointer-events: none;
            z-index: 100;
        }

        .crosshair-line {
            position: absolute;
            background: #ff0000;
            opacity: 0.9;
            box-shadow: 0 0 4px rgba(255, 0, 0, 0.5);
        }

        .crosshair-h {
            width: 24px;
            height: 3px;
            top: 10px;
            left: 0;
        }

        .crosshair-v {
            width: 3px;
            height: 24px;
            top: 0;
            left: 10px;
        }

        .crosshair-center {
            width: 4px;
            height: 4px;
            top: 10px;
            left: 10px;
            background: #ffffff;
            opacity: 0.7;
        }

        #menuScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a0000 0%, #000000 50%, #001a00 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: #fff;
            text-align: center;
        }

        #gameTitle {
            font-size: 48px;
            font-weight: bold;
            color: #ff0000;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.8);
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
        }

        #gameSubtitle {
            font-size: 24px;
            color: #ffff00;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            margin-bottom: 40px;
        }

        .menu-button {
            background: linear-gradient(to bottom, #ff0000 0%, #cc0000 100%);
            color: white;
            border: 3px solid #ffff00;
            padding: 15px 40px;
            font-size: 20px;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            text-transform: uppercase;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0,0,0,0.5);
        }

        .menu-button:hover {
            background: linear-gradient(to bottom, #ff3333 0%, #ff0000 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.7);
        }

        .controls-info {
            margin-top: 30px;
            font-size: 14px;
            color: #cccccc;
            max-width: 600px;
            line-height: 1.6;
        }

        .hidden {
            display: none;
        }

        #damageOverlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 0, 0, 0);
            pointer-events: none;
            transition: background 0.1s;
            z-index: 50;
        }

        #messages {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #ffff00;
            font-size: 18px;
            text-align: center;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            font-weight: bold;
            z-index: 60;
        }

        .message {
            background: rgba(0,0,0,0.8);
            padding: 12px 24px;
            margin: 8px 0;
            border: 2px solid #ffff00;
            border-radius: 0;
            animation: dukeMessage 4s ease-in-out;
            font-family: 'Courier New', monospace;
        }

        @keyframes dukeMessage {
            0% { opacity: 0; transform: translateY(-30px) scale(0.8); }
            15% { opacity: 1; transform: translateY(0) scale(1.1); }
            25% { transform: scale(1); }
            85% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: translateY(-20px) scale(0.9); }
        }

        .pickup-prompt {
            position: absolute;
            color: #00ff00;
            font-size: 16px;
            background: rgba(0,0,0,0.8);
            padding: 8px 16px;
            border: 2px solid #00ff00;
            pointer-events: none;
            animation: pickupPulse 1.5s infinite;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            z-index: 70;
        }

        @keyframes pickupPulse {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        /* Retro CRT effect */
        #gameCanvas {
            filter: contrast(1.3) brightness(1.1) saturate(1.2);
        }

        /* Weapon display in bottom right */
        #weaponSprite {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 200px;
            height: 150px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            pointer-events: none;
            z-index: 40;
            image-rendering: pixelated;
        }

        /* Muzzle flash overlay */
        #muzzleFlash {
            position: absolute;
            bottom: 80px;
            right: 20px;
            width: 250px;
            height: 200px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            pointer-events: none;
            z-index: 45;
            opacity: 0;
            image-rendering: pixelated;
        }

        /* Loading screen */
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            color: #fff;
        }

        .loading-text {
            font-size: 24px;
            color: #ffff00;
            margin-bottom: 20px;
        }

        .loading-bar {
            width: 400px;
            height: 20px;
            border: 2px solid #fff;
            background: #333;
        }

        .loading-fill {
            height: 100%;
            background: linear-gradient(to right, #ff0000, #ffff00, #00ff00);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
    
    <div id="ui">
        <div id="crosshair">
            <div class="crosshair-line crosshair-h"></div>
            <div class="crosshair-line crosshair-v"></div>
        </div>
        
        <div id="hud">
            <div>Health: <span id="health">100</span></div>
            <div>Ammo: <span id="ammo">30</span> / <span id="maxAmmo">30</span></div>
            <div id="reloadText" class="hidden" style="color: #cc1a1a;">RELOADING...</div>
        </div>
        
        <div id="weaponInfo">
            <div>Weapon: <span id="currentWeapon">Combat Rifle</span></div>
            <div>Damage: <span id="weaponDamage">25</span></div>
        </div>
        
        <div id="weaponSlots">
            <div class="weapon-slot" data-weapon="0">1</div>
            <div class="weapon-slot active" data-weapon="1">2</div>
            <div class="weapon-slot" data-weapon="2">3</div>
            <div class="weapon-slot" data-weapon="3">4</div>
            <div class="weapon-slot" data-weapon="4">5</div>
        </div>
        
        <div id="damageOverlay"></div>
        <div id="messages"></div>
    </div>

    <div id="instructions">
        <h2>🎮 DukeLiteAI Enhanced</h2>
        <p><strong>Welcome to the retro FPS experience!</strong></p>
        
        <div class="controls">
            <p><strong>CONTROLS:</strong></p>
            <p>• WASD - Move</p>
            <p>• Mouse - Look around</p>
            <p>• Left Click - Shoot</p>
            <p>• R - Reload</p>
            <p>• 1-5 - Switch weapons</p>
            <p>• Shift - Run</p>
            <p>• F - Pick up weapons</p>
        </div>
        
        <div class="controls">
            <p><strong>WEAPONS:</strong></p>
            <p>1. Pistol - Precise backup</p>
            <p>2. Rifle - Automatic workhorse</p>
            <p>3. Shotgun - Close-range devastation</p>
            <p>4. Sniper - Long-range elimination</p>
            <p>5. Plasma - Sci-fi energy weapon</p>
        </div>
        
        <button id="startButton">START GAME</button>
        <p style="font-size: 12px; margin-top: 15px; color: #999;">
            Click to lock mouse cursor for full FPS experience
        </p>
    </div>

    <script src="game.js"></script>
</body>
</html>
